// common
import itp from './itp';
import icafe from './icafe';
// front_qe_tools
import { frontQeToolsRoutes } from './front_qe_tools/index';
// rdEffectToolsRoutes
import { rdEffectToolsRoutes } from './rd_effect_tools/index';

export default [
    {
        path: '/',
        redirect: '/case/index'
    },
    {
        path: '/',
        component: '@/layouts/BasicLayout',
        routes: [
            itp,
            icafe,
            // 大前端质效工具模块
            ...frontQeToolsRoutes,
            // 研发提效
            ...rdEffectToolsRoutes,
            { path: '/*', component: '@/pages/404/404.jsx' }
        ]
    }
];
