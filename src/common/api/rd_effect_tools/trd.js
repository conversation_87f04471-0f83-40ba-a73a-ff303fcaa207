import { post } from 'COMMON/utils/requestUtils';
import { QILIN_BASICURL } from 'COMMON/config/baseUrl';
// 查询需求卡片关联的所有文档节点
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/ZV7hybZXiu/crf40rcgDbzXSx
export const getRelationDocList = (params) => {
    return post('/core/trd/relation/doc/list', params);
};

// 查询产品线关联的 TRD 树
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/ZV7hybZXiu/GfaBZDlJhdsy5x
export const getTrdTreeNodeId = (params) => {
    return post('/core/trd/tree/detail', params);
};

// 查询 TRD 列表
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/ZV7hybZXiu/1qSHWuecyk_Dfa
export const getTrdTree = (params) => {
    return post('/core/trd/tree/query', params);
};

// 创建一个需求文档节点
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/ZV7hybZXiu/aQXQ6Cj873ZJJQ
export const createMrd = (params) => {
    return post('/core/trd/node/create', params);
};

// 创建一个 TRD 执行任务
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/ZV7hybZXiu/0k6ahgx8uONONL
export const createTrdTask = (params) => {
    return post('/core/trd/task/create', params);
};

// 同步 TRD 更新内容
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/ZV7hybZXiu/Yaqy4-KlcmkAH2
export const updateTrd = (params) => {
    return post('/core/trd/node/update', params);
};

// 批量查询 TRD 的执行状态
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/ZV7hybZXiu/sQswCR5a0KtbrQ
export const getTrdTaskDetail = (params) => {
    return post('/core/trd/task/detail', params);
};

// PRD2TRD接口推荐列表交互接口kirin
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/JDyvmA9mML/ENj_2b-K0eIp3B
export const getKirin = (params) => {
    return post(QILIN_BASICURL + '/api/tool/214934', params);
};
