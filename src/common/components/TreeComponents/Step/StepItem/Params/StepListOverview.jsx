import { useState, useEffect } from 'react';
import classnames from 'classnames';
import { Row, Col, Popover, Image, Spin } from 'antd';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import { getName } from 'COMMON/components/TreeComponents/Step/utils';
import { getStepList } from 'COMMON/api/front_qe_tools/step';
import styles from './Params.module.less';
import { FileImageOutlined } from '@ant-design/icons';

function StepListOverview(props) {
    const { step, curOsType, snippetList } = props;
    const [templateDetail, setTemplateDetail] = useState(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        let templateCaseNodeId = (snippetList?.[curOsType] ?? [])?.find(
            (item) => item.templateId === step?.stepInfo.params.params.id
        )?.caseNodeId;
        if (templateCaseNodeId) {
            setLoading(true);
            getStepList({
                caseNodeId: +templateCaseNodeId,
                osType: curOsType
            })
                .then((res) => {
                    setTemplateDetail(res?.stepList ?? []);
                })
                .catch((err) => {
                    setTemplateDetail([]);
                })
                .finally(() => {
                    setLoading(false);
                });
        } else {
            setTemplateDetail([]);
        }
    }, [snippetList, step?.stepInfo?.params?.params?.id]);

    return (
        <Spin spinning={loading}>
            {templateDetail?.map((item, index) => {
                return (
                    <Row key={'templateStep_' + String(index)} className={styles.stepTemplateInfo}>
                        {/* step name */}
                        <Col flex="85px">
                            <span
                                className={classnames(
                                    styles.defaultStepName,
                                    styles.templateStepName
                                )}
                            >
                                {getName(
                                    item?.stepInfo?.type,
                                    item?.stepInfo?.params || item?.stepInfo,
                                    item?.stepType
                                )}
                            </span>
                        </Col>
                        {/* step desc */}
                        <Col flex="auto">
                            <span className={styles.desc}>
                                {'' === item?.stepDesc ? '步骤描述' : item?.stepDesc}
                            </span>
                            {item?.stepInfo?.params?.deviceInfo?.screenshot && (
                                <Popover
                                    title="截图"
                                    placement="right"
                                    content={
                                        <Image
                                            alt="建模图片"
                                            preview={false}
                                            width={200}
                                            src={item?.stepInfo?.params?.deviceInfo?.screenshot}
                                        />
                                    }
                                >
                                    <FileImageOutlined className={styles.imageIcon} />
                                </Popover>
                            )}
                        </Col>
                    </Row>
                );
            })}
        </Spin>
    );
}

export default connectModel([commonModel], (state) => ({
    snippetList: state.common.case.snippetList
}))(StepListOverview);
