// 智能测试白名单配置
import { getBoxLinkContent } from '../utils/utils';

const BOS_WHITELIST_URL = 'https://bj.bcebos.com/newmvp/qamate/profile/intelligent/moduleWhiteList.json';

// 智能测试白名单配置
let INTELLIGENT_WHITELIST = [];
let CHILD_INTELLIGENT_WHITELIST = [];

// 用例 智能测试白名单配置
let CASE_INTELLIGENT_WHITELIST = [];
let CHILD_CASE_INTELLIGENT_WHITELIST = [];

// 智能校验白名单配置
let SPECIAL_FEATURE_WHITELIST = [];
let CHILD_SPECIAL_FEATURE_WHITELIST = [];

// 智能定位白名单配置
let AI_LOCATE_WHITELIST = [];
let CHILD_AI_LOCATE_WHITELIST = [];

// 协同测试白名单配置
let COLLABORATIVE_TESTING_WHITELIST = [];
let CHILD_COLLABORATIVE_TESTING_WHITELIST = [];

// 从BOS链接加载白名单配置
export const loadWhitelistFromBOS = async () => {
    try {
        const whitelistData = await getBoxLinkContent(BOS_WHITELIST_URL);
        if (whitelistData) {
            // 智能测试白名单配置
            INTELLIGENT_WHITELIST = whitelistData?.INTELLIGENT_WHITELIST || [];
            // 子业务线 智能测试白名单配置
            CHILD_INTELLIGENT_WHITELIST = whitelistData?.CHILD_INTELLIGENT_WHITELIST || [];
            // 用例 智能测试白名单配置
            CASE_INTELLIGENT_WHITELIST = whitelistData?.CASE_INTELLIGENT_WHITELIST || [];
            // 子业务线 用例智能测试白名单配置
            CHILD_CASE_INTELLIGENT_WHITELIST = whitelistData?.CHILD_CASE_INTELLIGENT_WHITELIST || [];
            // 智能校验白名单配置
            SPECIAL_FEATURE_WHITELIST = whitelistData?.SPECIAL_FEATURE_WHITELIST || [];
            // 子业务线 智能校验白名单配置
            CHILD_SPECIAL_FEATURE_WHITELIST = whitelistData?.CHILD_SPECIAL_FEATURE_WHITELIST || [];
            // 智能定位白名单配置
            AI_LOCATE_WHITELIST = whitelistData?.AI_LOCATE_WHITELIST || [];
            // 子业务线 智能定位白名单配置
            CHILD_AI_LOCATE_WHITELIST = whitelistData?.CHILD_AI_LOCATE_WHITELIST || [];
            // 协同测试白名单配置
            COLLABORATIVE_TESTING_WHITELIST = whitelistData?.COLLABORATIVE_TESTING_WHITELIST || [];
            // 子业务线协同测试白名单配置
            CHILD_COLLABORATIVE_TESTING_WHITELIST = whitelistData?.CHILD_COLLABORATIVE_TESTING_WHITELIST || [];
        }
    } catch (error) {
        console.error('白名单bos链接，解析失败', error);
    }
};

// 根据当前业务线ID，在spaceList中查找其父业务线ID
export const findParentId = (currentSpaceId, spaceList) => {
    if (!spaceList || !currentSpaceId) {
        return null;
    }

    for (const parentSpace of spaceList) {
        if (parentSpace.children) {
            // 在父业务线的子业务线中 找到当前业务线ID
            const childFound = parentSpace.children.some((child) => child.id === currentSpaceId);
            if (childFound) {
                return parentSpace.id;
            }
        }
    }

    return null;
};

//  检查白名单函数
export const checkWhitelist = (currentSpace, spaceList, whitelist, childWhitelist) => {
    if (!currentSpace) {
        return false;
    }

    // 检查当前业务线ID是否在子业务线白名单中
    if (childWhitelist && childWhitelist.includes(currentSpace.id)) {
        return true;
    }

    // 获取父业务线ID
    const parentId = findParentId(currentSpace.id, spaceList);

    // 检查父业务线ID是否在白名单中
    return whitelist.includes(parentId);
};

// 检查当前业务线是否在智能测试白名单中
export const checkIntelligentWhitelist = (currentSpace, spaceList, checkIntelligentWhitelist = {}) => {
    // if (process.env.NODE_ENV !== 'production') {
    //     return true;
    // }
    return checkWhitelist(
        currentSpace,
        spaceList,
        checkIntelligentWhitelist?.INTELLIGENT_WHITELIST ?? INTELLIGENT_WHITELIST,
        checkIntelligentWhitelist?.CHILD_INTELLIGENT_WHITELIST ?? CHILD_INTELLIGENT_WHITELIST
    );
};

// 检查当前业务线是智能校验白名单中
export const intelligentCheckWhitelist = (currentSpace, spaceList) => {
    if (process.env.NODE_ENV !== 'production') {
        return true;
    }
    return checkWhitelist(currentSpace, spaceList, SPECIAL_FEATURE_WHITELIST, CHILD_SPECIAL_FEATURE_WHITELIST);
};

// 检查当前业务线是智能定位白名单中
export const checkAiLocateWhitelist = (currentSpace, spaceList) => {
    if (process.env.NODE_ENV !== 'production') {
        return true;
    }
    return checkWhitelist(currentSpace, spaceList, AI_LOCATE_WHITELIST, CHILD_AI_LOCATE_WHITELIST);
};

// 检查当前业务线是否在用例智能执行白名单中
export const checkCaseIntelligentWhitelist = (currentSpace, spaceList) => {
    if (process.env.NODE_ENV !== 'production') {
        return true;
    }
    return checkWhitelist(currentSpace, spaceList, CASE_INTELLIGENT_WHITELIST, CHILD_CASE_INTELLIGENT_WHITELIST);
};

// 检查当前业务线是否在协同测试白名单中
export const checkCollaborativeTestingWhitelist = (currentSpace, spaceList) => {
    if (process.env.NODE_ENV !== 'production') {
        return true;
    }
    return checkWhitelist(
        currentSpace,
        spaceList,
        COLLABORATIVE_TESTING_WHITELIST,
        CHILD_COLLABORATIVE_TESTING_WHITELIST
    );
};
