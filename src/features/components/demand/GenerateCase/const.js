export const getTips = (status) => {
    switch (status) {
        case 0:
            return '新建任务中';
        case 1:
            return '生成任务执行失败，Kirin 创建失败';
        case 2:
            return '执行中';
        case 3:
            return '执行中';
        case 4:
            return '生成任务执行失败';
        case 5:
            return '执行成功';
        case 6:
            return '生成任务执行失败，Kirin 执行超时';
        case 7:
            return '生成任务执行失败，兜底执行超时';
        default:
            return '';
    }
};

export const AGENT_OPTIONS = [
    {
        label: {
            text: '通用策略',
            tooltip: '通用策略-基于控件定位、支持回放'
        },
        value: 'autocase_generate_general'
    },
    {
        label: {
            text: '校验强化',
            tooltip: '校验强化-支持回放、强化UI缺陷检测'
        },
        value: 'autocase_generate_general_new_check'
    },
    {
        label: {
            text: '新执行模式',
            tooltip: '新执行模式-直接坐标操作、支持区域滑动查找等复杂操作、不可回放'
        },
        value: 'autocase_generate_general_coordinate'
    },
    {
        label: {
            text: '托管业务专门执行策略',
            tooltip: '只支持在『托管业务云端机房』下执行'
        },
        value: 'autocase_generate_tuoguan'
    },
    {
        label: {
            text: '电商业务专门执行策略',
            tooltip: '电商业务专用执行-支持请求调用操作'
        },
        value: 'autocase_generate_dianshang'
    },
    {
        label: {
            text: '小说（非劫持）专属策略',
            tooltip: '小说业务的非劫持类需求专属策略'
        },
        value: 'autocase_generate_novel_normal'
    },
];
