import { useState } from 'react';
import { Modal, Form, Select, Input, message } from 'antd';
import {submitApplication} from 'COMMON/api/qe_rag/workgroup';

import styles from '../common.module.less';

const { Option } = Select;
const { TextArea } = Input;

const ApplyGroupDialog = ({ 
  visible, 
  onCancel, 
  groupId, 
  groupName, 
  admins = [], 
  onSubmitSuccess 
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      await submitApplication({
        workGroupId: groupId,
        role: values.role,
        auditor: values.auditor,
        reason: values.reason
      });
      
      message.success('申请提交成功，等待审核');
      form.resetFields();
      onCancel();
      onSubmitSuccess?.();
    } catch (error) {
      console.error('申请提交失败:', error);
      message.error('申请提交失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={`申请加入 - ${groupName}`}
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      okText="提交申请"
      cancelText="取消"
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          role: 2 // 默认申请成员角色
        }}
      >
        <Form.Item
          name="role"
          label="申请角色"
          rules={[{ required: true, message: '请选择申请角色' }]}
        >
          <Select placeholder="请选择申请角色">
            <Option value={1}>管理员</Option>
            <Option value={2}>成员</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="auditor"
          label="选择审核人"
          rules={[{ required: true, message: '请选择审核人' }]}
        >
          <Select placeholder="请选择审核人">
            {admins.map((admin, index) => (
              <Option key={index} value={admin}>
                {admin}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="reason"
          label="申请理由"
        >
          <TextArea
            rows={4}
            placeholder="请输入申请理由（可选）"
            maxLength={200}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ApplyGroupDialog;