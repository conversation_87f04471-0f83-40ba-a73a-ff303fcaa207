import React, { useState, useEffect } from 'react';
import { Table, Tag, Button, message } from 'antd';
import { checkList, applyYes, applyNo, getUnreadMessages } from 'COMMON/api/qe_rag/workgroup';
import styles from './GroupTable.module.less';

const PendingApprovalTable = () => {
    const [tableData, setTableData] = useState([]);
    const [loading, setLoading] = useState(false);

    const getTableData = async () => {
        setLoading(true);
        try {
            // 调用API获取待审批数据
            const res = await checkList();
            setTableData(res.data || []);
        } catch (error) {
            console.error('获取待审批数据失败:', error);
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    };

    const handleApproval = async (row, type) => {
        try {
            const { id, workGroupId, role, userName, auditor, reason } = row;
            const params = {
                id,
                workGroupId,
                role,
                userName,
                auditor,
                reason
            };

            // 根据type调用不同的API
            const res = type === 0
                ? await applyYes(params)
                : await applyNo(params);

            if (res.code === 200) {
                message.success('提交成功');
                // 刷新未读消息数
                await getUnreadMessages();
            }

            getTableData();
        } catch (error) {
            console.error('审批操作失败:', error);
            message.error('提交失败');
        }
    };

    const columns = [
        {
            title: '组ID',
            dataIndex: 'workGroupId',
            key: 'workGroupId',
            ellipsis: true
        },
        {
            title: '组名称',
            dataIndex: 'workGroupName',
            key: 'workGroupName',
            ellipsis: true
        },
        {
            title: '申请角色',
            dataIndex: 'role',
            key: 'role',
            ellipsis: true,
            render: (role) => (
                <Tag 
                    size="small" 
                    color={+role === 1 ? 'blue' : 'green'}
                >
                    {+role === 1 ? '管理员' : '成员'}
                </Tag>
            )
        },
        {
            title: '申请原因',
            dataIndex: 'reason',
            key: 'reason',
            ellipsis: true
        },
        {
            title: '申请人',
            dataIndex: 'userName',
            key: 'userName',
            ellipsis: true
        },
        {
            title: '操作',
            key: 'action',
            fixed: 'right',
            width: 200,
            render: (_, record) => (
                <div>
                    <Button
                        size="small"
                        type="primary"
                        onClick={() => handleApproval(record, 0)}
                        style={{ marginRight: 8 }}
                    >
                        同意
                    </Button>
                    <Button
                        size="small"
                        danger
                        onClick={() => handleApproval(record, 1)}
                    >
                        拒绝
                    </Button>
                </div>
            )
        }
    ];

    useEffect(() => {
        getTableData();
    }, []);

    return (
        <Table
            columns={columns}
            dataSource={tableData}
            loading={loading}
            pagination={false}
            rowKey="id"
            scroll={{ x: 800 }}
        />
    );
};

export default PendingApprovalTable;