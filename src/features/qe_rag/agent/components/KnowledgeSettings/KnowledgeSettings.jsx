import React from 'react';
import { InputNumber, Button, Table, Tag } from 'antd';
import { EditOutlined, FileTextOutlined } from '@ant-design/icons';
import styles from './KnowledgeSettings.module.less';

const KnowledgeSettings = ({
    recallCount,
    similarity,
    selectedKnowledgeBases,
    onUpdateRecallCount,
    onUpdateSimilarity,
    onOpenAssociationDialog
}) => {
    const columns = [
        {
            title: '',
            dataIndex: 'icon',
            key: 'icon',
            width: 40,
            render: () => <FileTextOutlined />
        },
        {
            title: '知识库名称',
            dataIndex: 'name',
            key: 'name'
        }
    ];

    return (
        <div className={styles.knowledgeSettings}>
            <div className={styles.sectionTitle}>
                知识库设置
                <Button 
                    type="link" 
                    icon={<EditOutlined />} 
                    className={styles.actionButton} 
                    onClick={onOpenAssociationDialog}
                >
                    关联知识库
                </Button>
            </div>
            
            <div className={styles.settingsContent}>
                <div className={styles.settingItem}>
                    <div className={styles.settingControl}>
                        <span className={styles.settingLabel}>召回数</span>
                        <InputNumber 
                            value={recallCount}
                            onChange={onUpdateRecallCount}
                            min={1}
                            max={10}
                            size="small"
                            className={styles.inputNumber}
                        />
                    </div>
                    <div className={styles.hintText}>
                        召回切片的字符总数不应超过所选模型上下文长度
                    </div>
                </div>

                <div className={styles.settingItem}>
                    <div className={styles.settingControl}>
                        <span className={styles.settingLabel}>相似度</span>
                        <InputNumber 
                            value={similarity}
                            onChange={onUpdateSimilarity}
                            min={0.1}
                            max={1}
                            step={0.1}
                            size="small"
                            className={styles.inputNumber}
                        />
                    </div>
                    <div className={styles.hintText}>
                        调整匹配分阈值以过滤得到最相关答案
                    </div>
                </div>

                <div className={styles.tableSection}>
                    <Table 
                        dataSource={selectedKnowledgeBases}
                        columns={columns}
                        locale={{ emptyText: '暂无关联知识库' }}
                        className={styles.knowledgeTable}
                        pagination={false}
                        size="small"
                        rowKey="id"
                    />
                </div>
            </div>
        </div>
    );
};

export default KnowledgeSettings;
