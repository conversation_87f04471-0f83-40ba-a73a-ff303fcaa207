import React, { useState, useEffect, useMemo } from 'react';
import { Modal, Input, Button } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import styles from './MCPServerDialog.module.less';

const MCPServerDialog = ({
    visible,
    onClose,
    mcpServers = [],
    selectedServersInOrder = [],
    onConfirm
}) => {
    const [searchKeyword, setSearchKeyword] = useState('');
    const [localSelectedServers, setLocalSelectedServers] = useState([]);
    const [nextOrder, setNextOrder] = useState(1);

    // 初始化本地选中状态
    useEffect(() => {
        if (visible) {
            setLocalSelectedServers([...selectedServersInOrder]);
            setNextOrder(selectedServersInOrder.length + 1);
        }
    }, [visible, selectedServersInOrder]);

    // 过滤服务器列表
    const filteredMcpServers = useMemo(() => {
        if (!searchKeyword) {
            return mcpServers;
        }
        const keyword = searchKeyword.toLowerCase();
        return mcpServers.filter(
            (server) =>
                server.name.toLowerCase().includes(keyword) ||
                server.description.toLowerCase().includes(keyword)
        );
    }, [mcpServers, searchKeyword]);

    // 排序后的已选服务器
    const sortedSelectedServers = useMemo(() => {
        return [...localSelectedServers].sort((a, b) => a.order - b.order);
    }, [localSelectedServers]);

    // 检查服务器是否已选中
    const isServerSelected = (serverId) => {
        return localSelectedServers.some((item) => item.id === serverId);
    };

    // 获取服务器的顺序号
    const getServerOrder = (serverId) => {
        const found = localSelectedServers.find((item) => item.id === serverId);
        return found ? found.order : '';
    };

    // 获取服务器名称
    const getMCPServerName = (serverId) => {
        const server = mcpServers.find((s) => s.id === serverId);
        return server ? server.name : `服务器 ${serverId}`;
    };

    // 切换服务器选中状态
    const toggleServer = (serverId, checked) => {
        if (checked) {
            // 添加服务器到选择列表
            const server = mcpServers.find((s) => s.id === serverId);
            setLocalSelectedServers((prev) => [
                ...prev,
                {
                    id: serverId,
                    order: nextOrder,
                    description: server.description || ''
                }
            ]);
            setNextOrder((prev) => prev + 1);
        } else {
            // 从选择列表中移除服务器
            const index = localSelectedServers.findIndex((item) => item.id === serverId);
            if (index !== -1) {
                const newSelected = [...localSelectedServers];
                newSelected.splice(index, 1);
                setLocalSelectedServers(newSelected);
                // 重新排序
                reorderSelectedServers(newSelected);
            }
        }
    };

    // 重新排序选中的服务器
    const reorderSelectedServers = (servers = localSelectedServers) => {
        // 重新排序，确保序号连续
        const sorted = servers.sort((a, b) => a.order - b.order);
        // 更新序号
        const reordered = sorted.map((item, index) => ({
            ...item,
            order: index + 1
        }));

        setLocalSelectedServers(reordered);
        setNextOrder(reordered.length + 1);
    };

    // 确认选择
    const handleConfirm = () => {
        onConfirm(localSelectedServers);
        onClose();
    };

    // 取消选择
    const handleCancel = () => {
        setSearchKeyword('');
        onClose();
    };

    return (
        <Modal
            title="关联MCP服务器"
            open={visible}
            onCancel={handleCancel}
            width={600}
            destroyOnClose
            footer={[
                <Button key="cancel" onClick={handleCancel}>
                    取消
                </Button>,
                <Button
                    key="confirm"
                    type="primary"
                    onClick={handleConfirm}
                    disabled={localSelectedServers.length === 0}
                >
                    确定
                </Button>
            ]}
        >
            <div className={styles.dialogContent}>
                {/* 搜索框 */}
                <div className={styles.searchSection}>
                    <Input
                        placeholder="搜索 MCP Server"
                        prefix={<SearchOutlined />}
                        value={searchKeyword}
                        onChange={(e) => setSearchKeyword(e.target.value)}
                        allowClear
                    />
                </div>

                {/* 已选服务器执行顺序展示 */}
                {sortedSelectedServers.length > 0 && (
                    <div className={styles.selectedServersDisplay}>
                        <div className={styles.selectedServersHeader}>
                            <span>已选执行顺序</span>
                        </div>
                        <div className={styles.selectedServersList}>
                            {sortedSelectedServers.map((item) => (
                                <div key={item.id} className={styles.selectedServerTag}>
                                    <span className={styles.serverOrderBadge}>{item.order}</span>
                                    <span className={styles.selectedServerName}>
                                        {getMCPServerName(item.id)}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* 服务器列表 */}
                <div className={styles.serverListContainer}>
                    <div className={styles.serverListHeader}>
                        <span>可选 MCP Server （选择顺序决定执行顺序）</span>
                        <span className={styles.serverCount}>{filteredMcpServers.length}个</span>
                    </div>

                    <div className={styles.serverListContent}>
                        {filteredMcpServers.map((server) => (
                            <div key={server.id} className={styles.serverItem}>
                                <div className={styles.serverCheckbox}>
                                    <div
                                        className={`${styles.customOrderCheckbox} ${
                                            isServerSelected(server.id) ? styles.isSelected : ''
                                        }`}
                                        onClick={() =>
                                            toggleServer(server.id, !isServerSelected(server.id))
                                        }
                                    >
                                        {isServerSelected(server.id) && (
                                            <span className={styles.checkboxNumber}>
                                                {getServerOrder(server.id)}
                                            </span>
                                        )}
                                    </div>
                                    <div className={styles.serverInfo}>
                                        <span className={styles.serverName}>
                                            {server.id} - {server.name} - {server.description}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </Modal>
    );
};

export default MCPServerDialog;
