import React, { useState, useEffect, useCallback, useRef } from 'react';
import { debounce, cloneDeep, merge, find, isEmpty } from 'lodash';
import { useNavigate } from 'umi';
import {
    Card,
    Form,
    Input,
    Select,
    Button,
    Tag,
    Row,
    Col,
    message,
    Space,
    Avatar,
    List,
    Upload,
    Tabs,
    Divider,
    Typography,
    Popover,
    Dropdown,
    Modal,
    Pagination,
    Badge,
    Empty,
    Switch,
    Descriptions,
    Spin,
    Tooltip
} from 'antd';
import {
    SearchOutlined,
    PlusOutlined,
    BookOutlined,
    UserOutlined,
    CalendarOutlined,
    FileTextOutlined,
    InboxOutlined,
    CloudUploadOutlined,
    SettingOutlined,
    MoreOutlined,
    DeleteOutlined,
    FilterOutlined,
    UploadOutlined,
    StopOutlined,
    PlayCircleOutlined
} from '@ant-design/icons';
import { stringifyUrl } from 'query-string';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import AddDocumentModal from './components/AddDocumentModal/AddDocumentModal';
import DocumentDetailForm from './components/DocumentDetailForm/DocumentDetailForm';
import {
    DOCUMENT_STATUSES,
    DEFAULT_BOOK,
    DEFAULT_PAGE_SIZE,
    DEFAULT_CURRENT_PAGE,
    DEFAULT_CUT_METHOD,
    DEFAULT_MAX_TOKENS,
    DEFAULT_FILTERS,
    DEFAULT_FILTER_QUERY,
    STATUS_COLOR_MAP,
    DEFAULT_SAFE_ITEM
} from './const';
import styles from './DocPage.module.less';
import {
    getDocuments,
    deleteDocument,
    updateDocumentStatus,
    updateDocument,
    createDocument,
    getDocumentDetail
} from 'COMMON/api/qe_rag/document';
import { getBook } from 'COMMON/api/qe_rag/book';

const { Option } = Select;
const { Dragger } = Upload;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

const DocumentManagePage = ({
    statuses = DOCUMENT_STATUSES,
    onRouteChange,
    allWorkGroupList,
    username
}) => {
    // 直接从URL解析docId
    const getDocIdFromUrl = () => {
        const urlParams = new URLSearchParams(window.location.search);

        // 处理hash路由中的参数，兼容错误格式（如 ?=knowledgeId=7&docId=65）
        let hashParamString = window.location.hash.split('?')[1] || '';

        // 修复可能的格式错误：如果参数字符串以 = 开头，去掉开头的 =
        if (hashParamString.startsWith('=')) {
            hashParamString = hashParamString.substring(1);
        }

        const hashParams = new URLSearchParams(hashParamString);
        return urlParams.get('docId') || hashParams.get('docId');
    };

    const [urlDocId, setUrlDocId] = useState(getDocIdFromUrl());
    // 筛选状态
    const [filters, setFilters] = useState(DEFAULT_FILTERS);

    const [selectedDoc, setSelectedDoc] = useState(null);
    const [searchText, setSearchText] = useState('');
    const [addModalVisible, setAddModalVisible] = useState(false);
    const [cutMethod, setCutMethod] = useState(DEFAULT_CUT_METHOD);
    const [maxTokens, setMaxTokens] = useState(DEFAULT_MAX_TOKENS);
    const [shouldSelectFirst, setShouldSelectFirst] = useState(false);

    // 数据状态管理
    const [items, setItems] = useState([]);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
    const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
    const [searchQuery, setSearchQuery] = useState('');
    const [filterQuery, setFilterQuery] = useState(DEFAULT_FILTER_QUERY);

    // 动态分页相关状态和ref
    const listContainerRef = useRef(null);
    const [dynamicPageSize, setDynamicPageSize] = useState(DEFAULT_PAGE_SIZE);
    const [containerHeight, setContainerHeight] = useState(0);
    // 文档详情状态
    const [documentDetail, setDocumentDetail] = useState(null);
    const [detailLoading, setDetailLoading] = useState(false);
    const [editLoading, setEditLoading] = useState(false);

    // 模型列表状态
    // const [modelsLoading, setModelsLoading] = useState(false);

    // 知识库默认值状态
    const [knowledgeBaseDefaults, setKnowledgeBaseDefaults] = useState(null);
    const [defaultsLoading, setDefaultsLoading] = useState(false);

    // 在状态管理部分添加切片分页状态
    const [chunkPagination, setChunkPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    const query = getQueryParams();
    const navigate = useNavigate();
    // 监听URL变化
    useEffect(() => {
        const handleUrlChange = () => {
            const newDocId = getDocIdFromUrl();
            setUrlDocId(newDocId);
        };

        // 监听popstate事件（浏览器前进后退）
        window.addEventListener('popstate', handleUrlChange);
        // 监听hashchange事件（hash路由变化）
        window.addEventListener('hashchange', handleUrlChange);

        return () => {
            window.removeEventListener('popstate', handleUrlChange);
            window.removeEventListener('hashchange', handleUrlChange);
        };
    }, []);
    // 计算动态分页大小
    const calculateDynamicPageSize = useCallback(() => {
        if (!listContainerRef.current) {
            return DEFAULT_PAGE_SIZE;
        }

        const container = listContainerRef.current;
        const containerHeight = container.clientHeight;

        // 每个列表项的实际高度（根据样式分析）
        // List.Item的padding是5px上下，marginBottom是4px，内容高度约30px
        const itemHeight = 44; // 实际测量约44px每个item

        // 计算能容纳的最大item数量，至少保证显示5个
        const maxItems = Math.max(5, Math.floor(containerHeight / itemHeight));

        return maxItems;
    }, [pageSize]);

    // 初始化时计算动态分页大小（只执行一次）
    useEffect(() => {
        const initializePageSize = () => {
            if (!listContainerRef.current) {
                return;
            }

            const newPageSize = calculateDynamicPageSize();

            if (newPageSize > 0 && newPageSize !== pageSize) {
                setDynamicPageSize(newPageSize);
                setPageSize(newPageSize);
                setCurrentPage(1);
            }
        };

        // 延迟执行，确保DOM已经渲染完成
        const timer = setTimeout(initializePageSize, 800);

        return () => {
            clearTimeout(timer);
        };
    }, []); // 空依赖数组，只在组件挂载时执行一次

    // 计算当前页显示的切片数据
    const getCurrentChunks = () => {
        if (!documentDetail?.contentList) {
            return [];
        }
        const { current, pageSize } = chunkPagination;
        const startIndex = (current - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        return documentDetail.contentList.slice(startIndex, endIndex);
    };

    // 处理切片分页变化
    const handleChunkPageChange = (page, size) => {
        setChunkPagination((prev) => ({
            ...prev,
            current: page,
            pageSize: size || prev.pageSize
        }));
    };

    // 更新切片总数（在获取文档详情后调用）
    useEffect(() => {
        if (documentDetail?.contentList) {
            setChunkPagination((prev) => ({
                ...prev,
                total: documentDetail.contentList.length,
                current: 1 // 重置到第一页
            }));
        }
    }, [documentDetail?.contentList]);

    // 独立的数据获取函数
    const fetchDocuments = useCallback(
        async (overrideParams = {}) => {
            setLoading(true);
            try {
                const queryParams = {
                    title: filterQuery.title || searchQuery,
                    owner: filterQuery.owner,
                    status: filterQuery.status,
                    id: filterQuery.id,
                    knowledgeBaseId: query?.knowledgeId || 64,
                    page: currentPage,
                    size: pageSize,
                    ...overrideParams
                };
                const result = await getDocuments(queryParams);

                setItems(result.items || []);
                setTotal(result.total || 0);
            } finally {
                setLoading(false);
            }
        },
        [searchQuery, filterQuery, currentPage, pageSize, knowledgeBaseDefaults?.id]
    );

    // 获取嵌入模型列表
    // const fetchModels = useCallback(async () => {
    //     setModelsLoading(true);
    //     try {
    //         // 使用新的API调用方式
    //         const requestData = {
    //             isDelete: 0, // 0表示未删除的模型
    //             type: 0 // 0表示嵌入模型，1表示聊天模型
    //         };
    //         const response = await getEmbeddingModels(requestData);

    //         if (response) {
    //             setModels(response || []);
    //         } else {
    //             setModels([]);
    //         }
    //     } catch (error) {
    //         message.error('获取模型列表失败: ' + (error.message || '未知错误'));
    //         setModels([]);
    //     } finally {
    //         setModelsLoading(false);
    //     }
    // }, []);

    // 获取知识库默认值
    const fetchKnowledgeBaseDefaults = useCallback(async () => {
        if (!query?.knowledgeId) {
            return;
        }
        setDefaultsLoading(true);
        try {
            const res = await getBook(query?.knowledgeId);
            const embeddingRule = JSON.parse(res.embeddingRule || '{}');
            const defaults = {
                ...res, // 保留所有原始数据
                parseId: res.parseId || 1,
                delimiter: embeddingRule.delimiter || ['。', '！'],
                chunkTokenNum: embeddingRule.chunkTokenNum || 600,
                embeddingModelId: res.embeddingModelId,
                cronOpen: false,
                cronExpression: ''
            };

            setKnowledgeBaseDefaults(defaults);
        } finally {
            setDefaultsLoading(false);
        }
    }, [query?.knowledgeId]);

    // 初始化数据
    useEffect(() => {
        fetchDocuments();
        fetchKnowledgeBaseDefaults();
    }, [fetchDocuments, fetchKnowledgeBaseDefaults]);

    // 监听分页、搜索、筛选变化
    useEffect(() => {
        fetchDocuments();
    }, [currentPage, pageSize, searchQuery, filterQuery]);

    // 根据路由中的docId和items变化来处理文档选中
    useEffect(() => {
        if (!items || items.length === 0) {
            setSelectedDoc(null);
            return;
        }

        // 如果有路由中的docId，尝试选中对应的文档
        if (urlDocId) {
            const targetDoc = items.find((item) => item.id.toString() === urlDocId.toString());
            if (targetDoc) {
                // 只有当前选中的文档不是目标文档时才更新
                if (selectedDoc?.id !== targetDoc.id) {
                    setSelectedDoc(targetDoc);
                    // 获取文档详情
                    fetchDocumentDetail(targetDoc.id);
                }
                return;
            }
        }

        // 如果需要自动选中第一个文档（新建文档后）
        if (shouldSelectFirst) {
            handleDocumentSelect(items[0]);
            setShouldSelectFirst(false); // 重置状态
            return;
        }

        // 如果没有路由docId，清空选中状态
        if (!urlDocId) {
            setSelectedDoc(null);
        }
    }, [items, urlDocId, shouldSelectFirst]);

    // 防抖优化搜索
    const debouncedSearch = useCallback(
        debounce((searchValue) => {
            setSearchQuery(searchValue);
            setCurrentPage(1);
        }, 500),
        []
    );

    // 当搜索文本变化时，使用防抖处理
    useEffect(() => {
        debouncedSearch(searchText);
        // 清理函数
        return () => {
            debouncedSearch.cancel();
        };
    }, [searchText, debouncedSearch]);

    // 使用lodash优化筛选条件更新
    const updateFilter = useCallback((key, value) => {
        setFilters((prev) => merge(cloneDeep(prev), { [key]: value }));
    }, []);

    // 重置筛选条件
    const resetFilters = useCallback(() => {
        setFilters(cloneDeep(DEFAULT_FILTERS));
        setFilterQuery(cloneDeep(DEFAULT_FILTER_QUERY));
        setCurrentPage(1);
    }, []);

    // 处理返回按钮点击
    const handleGoBack = () => {
        navigate(
            stringifyUrl({
                url: '/qe_rag/knowledge'
            })
        );
    };

    // 使用lodash检查是否有筛选条件被选中
    const hasActiveFilters = () => {
        const activeFilters = {
            ...filterQuery,
            ...filters
        };
        // 移除空值和null值，然后检查是否还有有效值
        return !isEmpty(
            Object.values(activeFilters).filter(
                (value) => value !== '' && value !== null && value !== undefined
            )
        );
    };

    // 获取状态标签
    const getStatusTag = (status, _isSelected = false) => {
        const statusInfo = statuses.find((item) => item.key === status);
        const statusText = statusInfo ? statusInfo.value : '';

        switch (status) {
            case 1:
            case 5:
                return <Tag color="orange">{statusText}</Tag>;
            case 2:
                return <Tag color="green">{statusText}</Tag>;
            case 3:
                return <Tag color="red">{statusText}</Tag>;
            case 4:
                return <Tag color="default">{statusText}</Tag>;
            default:
                return <Tag>{statusText}</Tag>;
        }
    };

    // 处理文档详情保存
    const handleDocumentSave = async (updateData) => {
        try {
            setEditLoading(true);
            await updateDocument(updateData);

            message.success('文档更新成功');

            // 重新获取文档详情
            await fetchDocumentDetail(documentDetail.id);

            await fetchDocuments();
        } finally {
            setEditLoading(false);
        }
    };

    // 获取文档详情
    const fetchDocumentDetail = useCallback(async (docId) => {
        setDetailLoading(true);
        try {
            const response = await getDocumentDetail({ id: docId });
            setDocumentDetail(response);
        } finally {
            setDetailLoading(false);
        }
    }, []);

    // 处理文档选择
    const handleDocumentSelect = (doc) => {
        // 检查是否已经选中了同一个文档，避免重复处理
        if (selectedDoc?.id === doc.id) {
            return;
        }

        setSelectedDoc(doc);

        // 获取文档详情
        fetchDocumentDetail(doc.id);

        // 更新路由，添加 docId 参数，保留 knowledgeId
        // const currentUrl = new URL(window.location.href);
        // const currentParams = Object.fromEntries(currentUrl.searchParams.entries());

        // 使用 navigate 更新路由，保留原有参数并添加 docId
        navigate(
            stringifyUrl({
                url: '/qe_rag/knowledge/docs',
                query: {
                    ...query,
                    docId: doc.id
                }
            })
        );

        // 触发路由变化回调（通知Vue组件更新路由）
        onRouteChange?.(doc.id);
    };

    // 处理删除文档
    const handleDeleteDocument = async (docId, docTitle) => {
        const body = {
            id: docId,
            owner: 'xushixuan01'
        };
        await deleteDocument(body);

        message.success(`文档 "${docTitle}" 删除成功`);

        // 如果删除的是当前选中的文档，清空选中状态并更新路由
        if (selectedDoc?.id === docId) {
            setSelectedDoc(null);
            setDocumentDetail(null);

            // 清除路由中的 docId 参数，保留其他参数（如 knowledgeId）
            const currentUrl = new URL(window.location.href);
            const currentParams = Object.fromEntries(currentUrl.searchParams.entries());
            delete currentParams.docId;

            navigate(
                stringifyUrl({
                    url: '/qe_rag/knowledge/docs',
                    query: currentParams
                })
            );
        }

        await fetchDocuments();
    };

    // 处理文档状态更新（禁用/启用）
    const handleUpdateDocumentStatus = async (docId, docTitle, newStatus, actionName) => {
        try {
            const body = {
                id: docId,
                // owner: username,
                owner: 'xushixuan01',
                status: newStatus
            };
            // 调用状态更新API
            await updateDocumentStatus(body);

            // 显示成功消息
            message.success(`文档 "${docTitle}" ${actionName}成功`);

            // 状态更新成功后刷新列表
            await fetchDocuments();
        } catch (error) {
            message.error(`${actionName}文档 "${docTitle}" 失败: ${error.message || '未知错误'}`);
        }
    };

    // 处理文档创建成功
    const handleAddDocumentSuccess = async (newDocId) => {
        setAddModalVisible(false);

        // 刷新文档列表
        await fetchDocuments();

        // 等待一个短暂的时间确保数据更新完成
        await new Promise((resolve) => setTimeout(resolve, 100));

        // 设置标志，在数据更新后自动选中第一个文档
        setShouldSelectFirst(true);
    };

    // 处理分页变化
    const handlePageChange = (page, size) => {
        setCurrentPage(page);
        if (size && size !== pageSize) {
            setPageSize(size);
        }
    };
    // 处理添加文档
    const handleAddDocument = () => {
        setAddModalVisible(true);
    };

    // 格式化工作组信息显示
    const formatGroupInfo = (groupId) => {
        if (!groupId) {
            return '-';
        }

        // 在工作组列表中查找对应的工作组
        const group = allWorkGroupList?.find((g) => g.id === groupId);

        return group ? (
            <span>
                <span className={styles.groupIdBadge}>{groupId}</span>
                {group.name}
            </span>
        ) : (
            '-'
        );
    };

    // 使用独立的状态数据
    const displayItems = items;
    const displayTotal = total;
    const displayLoading = loading;
    const displayCurrentPage = currentPage;
    const displayPageSize = pageSize;

    return (
        <>
            <div className={styles.mainContainer}>
                {/* 顶部知识库信息 */}
                <Card className={styles.knowledgeBaseCard}>
                    <Row align="middle" justify="space-between">
                        <Col>
                            <Space size="large">
                                {/* 返回按钮 */}
                                <Button
                                    type="text"
                                    icon={<span className={styles.backButtonIcon}>←</span>}
                                    onClick={handleGoBack}
                                    className={styles.backButton}
                                >
                                    返回
                                </Button>
                                <div>
                                    <div className={styles.headerInfo}>
                                        <div>
                                            <Tag color="blue">知识库</Tag>
                                        </div>

                                        <div className={styles.knowledgeBaseName}>
                                            {knowledgeBaseDefaults?.name}
                                        </div>
                                    </div>

                                    <Space className={styles.headerSpace}>
                                        <Text className={styles.headerText}>
                                            <UserOutlined /> 工作组：
                                            {formatGroupInfo(knowledgeBaseDefaults?.groupId)}
                                        </Text>
                                        <Text className={styles.headerTextWithMargin}>
                                            <CalendarOutlined /> 创建时间:{' '}
                                            {knowledgeBaseDefaults?.createAt}
                                        </Text>
                                    </Space>
                                </div>
                            </Space>
                        </Col>
                        <Col>
                            <Button
                                type="primary"
                                size="large"
                                icon={<PlusOutlined />}
                                onClick={handleAddDocument}
                                className={styles.addDocumentButton}
                            >
                                添加文档
                            </Button>
                        </Col>
                    </Row>
                </Card>

                {/* 主要内容区域 */}
                <Row gutter={8} className={styles.mainContentRow}>
                    {/* 左侧文档列表 */}
                    <Col span={8}>
                        <Card
                            title={
                                <div className={styles.searchFilterContainer}>
                                    {/* 搜索框 */}
                                    <div className={styles.searchContainer}>
                                        <Input
                                            placeholder="输入文档名称..."
                                            value={searchText}
                                            onChange={(e) => setSearchText(e.target.value)}
                                            prefix={<SearchOutlined />}
                                            className={styles.searchInput}
                                        />
                                    </div>

                                    {/* 筛选按钮 */}
                                    <Popover
                                        getPopupContainer={() => document.body}
                                        content={
                                            <div className={styles.filterPopoverContent}>
                                                <Form size="small">
                                                    <Row gutter={[8, 8]} align="middle">
                                                        <Col span={5}>
                                                            <span className={styles.filterLabel}>
                                                                文档ID
                                                            </span>
                                                        </Col>
                                                        <Col span={19}>
                                                            <Input
                                                                placeholder="请输入文档ID"
                                                                value={filters.documentId}
                                                                onChange={(e) =>
                                                                    updateFilter(
                                                                        'documentId',
                                                                        e.target.value
                                                                    )
                                                                }
                                                                allowClear
                                                            />
                                                        </Col>
                                                    </Row>
                                                    <Row
                                                        gutter={[8, 5]}
                                                        className={styles.filterRowMargin}
                                                        align="middle"
                                                    >
                                                        <Col span={5}>
                                                            <span className={styles.filterLabel}>
                                                                创建人
                                                            </span>
                                                        </Col>
                                                        <Col span={19}>
                                                            <Input
                                                                placeholder="请输入创建人"
                                                                value={filters.creator}
                                                                onChange={(e) =>
                                                                    updateFilter(
                                                                        'creator',
                                                                        e.target.value
                                                                    )
                                                                }
                                                                allowClear
                                                            />
                                                        </Col>
                                                    </Row>

                                                    <Row
                                                        gutter={[8, 8]}
                                                        className={styles.filterRowMargin}
                                                        align="middle"
                                                    >
                                                        <Col span={5}>
                                                            <span className={styles.filterLabel}>
                                                                &nbsp; &nbsp;状态
                                                            </span>
                                                        </Col>
                                                        <Col span={19}>
                                                            <Select
                                                                placeholder="请选择状态"
                                                                value={filters.status}
                                                                onChange={(value) =>
                                                                    updateFilter('status', value)
                                                                }
                                                                onClear={() =>
                                                                    updateFilter('status', null)
                                                                }
                                                                className={styles.filterSelectWidth}
                                                                allowClear
                                                                getPopupContainer={() =>
                                                                    document.body
                                                                }
                                                            >
                                                                {statuses.map((status) => (
                                                                    <Option
                                                                        key={status.key}
                                                                        value={status.key}
                                                                    >
                                                                        {status.value}
                                                                    </Option>
                                                                ))}
                                                            </Select>
                                                        </Col>
                                                    </Row>

                                                    <div className={styles.filterActions}>
                                                        <Button size="small" onClick={resetFilters}>
                                                            重置
                                                        </Button>
                                                        <Button
                                                            type="primary"
                                                            size="small"
                                                            onClick={() => {
                                                                console.log('应用筛选:', filters);
                                                                setFilterQuery({
                                                                    title:
                                                                        filters.documentName || '',
                                                                    owner: filters.creator || '',
                                                                    status: filters.status || '',
                                                                    id: filters.documentId || ''
                                                                });
                                                                setCurrentPage(1); // 筛选时重置到第一页
                                                            }}
                                                        >
                                                            确定
                                                        </Button>
                                                    </div>
                                                </Form>
                                            </div>
                                        }
                                        title="筛选条件"
                                        trigger="click"
                                        placement="right"
                                        zIndex={1000}
                                        styles={{
                                            content: { maxWidth: '350px' }
                                        }}
                                    >
                                        <Badge dot={hasActiveFilters()}>
                                            <FilterOutlined className={styles.filterIcon} />
                                        </Badge>
                                    </Popover>
                                </div>
                            }
                            className={styles.documentListCard}
                            styles={{
                                header: {
                                    padding: '16px 16px 8px 16px'
                                },
                                body: {
                                    padding: '8px 16px 16px 16px',
                                    flex: 1,
                                    overflow: 'hidden',
                                    display: 'flex',
                                    flexDirection: 'column'
                                }
                            }}
                        >
                            <div className={styles.listMainContainer}>
                                {/* 文档列表 */}
                                <div ref={listContainerRef} className={styles.listScrollContainer}>
                                    {/* 表头 */}
                                    <div className={styles.tableHeader}>
                                        <div className={styles.tableHeaderId}>ID</div>
                                        <div className={styles.tableHeaderTitle}>文档名</div>
                                        <div className={styles.tableHeaderType}>类型</div>
                                        <div className={styles.tableHeaderStatus}>状态</div>
                                        <div className={styles.tableHeaderActions}></div>
                                    </div>

                                    <List
                                        loading={displayLoading}
                                        dataSource={displayItems}
                                        renderItem={(item, index) => {
                                            const safeItem = {
                                                id: item.id || `item-${index}`,
                                                title: item.title || '未命名文档',
                                                type: item.type || '未知类型',
                                                status: item.status || 1,
                                                owner: item.owner || '未知',
                                                createAt: item.createAt || '未知时间'
                                            };

                                            return (
                                                <List.Item
                                                    key={safeItem.id}
                                                    onClick={() => handleDocumentSelect(safeItem)}
                                                    className={`${styles.documentListItem} ${
                                                        selectedDoc?.id === safeItem.id
                                                            ? `${styles.documentListItemSelected} ${styles.documentItemSelected}`
                                                            : `${styles.documentListItemNormal} ${styles.documentItem}`
                                                    }`}
                                                >
                                                    <div className={styles.documentItemContent}>
                                                        <div className={styles.documentItemLeft}>
                                                            <List.Item.Meta
                                                                title={
                                                                    <Tooltip
                                                                        title={safeItem.title}
                                                                        placement="top"
                                                                    >
                                                                        <div
                                                                            className={`${
                                                                                styles.documentItemTitle
                                                                            } ${
                                                                                selectedDoc?.id ===
                                                                                safeItem.id
                                                                                    ? styles.documentItemTitleSelected
                                                                                    : styles.documentItemTitleNormal
                                                                            }`}
                                                                        >
                                                                            <span
                                                                                className={
                                                                                    styles.documentIdBadge
                                                                                }
                                                                            >
                                                                                {safeItem.id}
                                                                            </span>
                                                                            {safeItem.title}
                                                                        </div>
                                                                    </Tooltip>
                                                                }
                                                            />
                                                        </div>

                                                        {/* 右侧状态、Tag和菜单 */}
                                                        <div className={styles.documentItemRight}>
                                                            <Tag
                                                                color={'blue'}
                                                                className={styles.documentTypeTag}
                                                            >
                                                                {safeItem.type}
                                                            </Tag>
                                                            {getStatusTag(
                                                                safeItem.status,
                                                                selectedDoc?.id === safeItem.id
                                                            )}

                                                            <Dropdown
                                                                menu={{
                                                                    items: [
                                                                        // 禁用选项 - 只有切片成功(status=2)的文档才显示
                                                                        ...(safeItem.status === 2
                                                                            ? [
                                                                                  {
                                                                                      key: 'disable',
                                                                                      label: (
                                                                                          <span
                                                                                              className={
                                                                                                  styles.dropdownItemDisable
                                                                                              }
                                                                                          >
                                                                                              <StopOutlined
                                                                                                  className={
                                                                                                      styles.dropdownIconMargin
                                                                                                  }
                                                                                              />
                                                                                              禁用
                                                                                          </span>
                                                                                      ),
                                                                                      onClick:
                                                                                          () => {
                                                                                              handleUpdateDocumentStatus(
                                                                                                  safeItem.id,
                                                                                                  safeItem.title,
                                                                                                  4, // 状态4表示已禁用
                                                                                                  '禁用'
                                                                                              );
                                                                                          }
                                                                                  }
                                                                              ]
                                                                            : []),
                                                                        // 启用选项 - 只有已禁用(status=4)的文档才显示
                                                                        ...(safeItem.status === 4
                                                                            ? [
                                                                                  {
                                                                                      key: 'enable',
                                                                                      label: (
                                                                                          <span
                                                                                              className={
                                                                                                  styles.dropdownItemEnable
                                                                                              }
                                                                                          >
                                                                                              <PlayCircleOutlined
                                                                                                  className={
                                                                                                      styles.dropdownIconMargin
                                                                                                  }
                                                                                              />
                                                                                              启用
                                                                                          </span>
                                                                                      ),
                                                                                      onClick:
                                                                                          () => {
                                                                                              handleUpdateDocumentStatus(
                                                                                                  safeItem.id,
                                                                                                  safeItem.title,
                                                                                                  2, // 状态2表示切片成功（启用）
                                                                                                  '启用'
                                                                                              );
                                                                                          }
                                                                                  }
                                                                              ]
                                                                            : []),
                                                                        // 删除选项 - 所有文档都显示
                                                                        {
                                                                            key: 'delete',
                                                                            label: (
                                                                                <span
                                                                                    className={
                                                                                        styles.dropdownItemDelete
                                                                                    }
                                                                                >
                                                                                    <DeleteOutlined
                                                                                        className={
                                                                                            styles.dropdownIconMargin
                                                                                        }
                                                                                    />
                                                                                    删除
                                                                                </span>
                                                                            ),
                                                                            onClick: () => {
                                                                                Modal.confirm({
                                                                                    title: '确认删除',
                                                                                    content: `确定要删除文档 "${safeItem.title}" 吗？此操作不可恢复。`,
                                                                                    okText: '确定',
                                                                                    cancelText:
                                                                                        '取消',
                                                                                    okType: 'danger',
                                                                                    onOk: () => {
                                                                                        handleDeleteDocument(
                                                                                            safeItem.id,
                                                                                            safeItem.title
                                                                                        );
                                                                                    }
                                                                                });
                                                                            }
                                                                        }
                                                                    ]
                                                                }}
                                                                trigger={['click']}
                                                                placement="bottomRight"
                                                                getPopupContainer={() =>
                                                                    document.body
                                                                }
                                                            >
                                                                <Button
                                                                    type="text"
                                                                    size="small"
                                                                    icon={
                                                                        <MoreOutlined
                                                                            className={
                                                                                selectedDoc?.id ===
                                                                                safeItem.id
                                                                                    ? styles.moreIconSelected
                                                                                    : styles.moreIcon
                                                                            }
                                                                        />
                                                                    }
                                                                    onClick={(e) =>
                                                                        e.stopPropagation()
                                                                    }
                                                                />
                                                            </Dropdown>
                                                        </div>
                                                    </div>
                                                </List.Item>
                                            );
                                        }}
                                    />
                                </div>

                                {/* 分页 - 只有多页时才显示 */}
                                {displayTotal > displayPageSize && (
                                    <div className={styles.paginationContainer}>
                                        <Pagination
                                            current={displayCurrentPage}
                                            pageSize={displayPageSize}
                                            total={displayTotal}
                                            onChange={handlePageChange}
                                            simple
                                            // showSizeChanger
                                            // showQuickJumper
                                            // showTotal={(total, range) =>
                                            //     `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                                            // }
                                            size="small"
                                        />
                                    </div>
                                )}
                            </div>
                        </Card>
                    </Col>

                    {/* 右侧详情面板 */}
                    <Col span={16}>
                        <Card className={styles.rightPanelCard}>
                            {selectedDoc ? (
                                <Tabs
                                    defaultActiveKey="overview"
                                    size="small"
                                    className={styles.tabsContainer}
                                    tabBarStyle={{
                                        margin: 0,
                                        paddingLeft: '20px',
                                        paddingRight: '20px'
                                    }}
                                >
                                    <TabPane tab="概览" key="overview">
                                        <div className={styles.tabPaneContent}>
                                            <Spin spinning={detailLoading}>
                                                {documentDetail ? (
                                                    <div className={styles.documentDetailContainer}>
                                                        {/* 基本信息 */}
                                                        <div className={styles.basicInfoSection}>
                                                            <Title
                                                                level={5}
                                                                className={styles.sectionTitle}
                                                            >
                                                                基本信息
                                                            </Title>

                                                            <Descriptions
                                                                title=""
                                                                className={
                                                                    styles.descriptionsContainer
                                                                }
                                                            >
                                                                <Descriptions.Item label="文档ID">
                                                                    {documentDetail.id}
                                                                </Descriptions.Item>

                                                                <Descriptions.Item
                                                                    label="文档标题"
                                                                    className={
                                                                        styles.descriptionItem
                                                                    }
                                                                >
                                                                    <Tooltip
                                                                        title={documentDetail.title}
                                                                    >
                                                                        <div
                                                                            className={
                                                                                styles.descriptionItemContent
                                                                            }
                                                                        >
                                                                            {documentDetail.title}
                                                                        </div>
                                                                    </Tooltip>
                                                                </Descriptions.Item>

                                                                <Descriptions.Item label="文档类型">
                                                                    {documentDetail.type}
                                                                </Descriptions.Item>
                                                                <Descriptions.Item label="创建人">
                                                                    {documentDetail.owner}
                                                                </Descriptions.Item>
                                                                <Descriptions.Item label="创建时间">
                                                                    {documentDetail.createAt}
                                                                </Descriptions.Item>
                                                                <Descriptions.Item label="更新时间">
                                                                    {documentDetail.updateAt}
                                                                </Descriptions.Item>
                                                            </Descriptions>
                                                        </div>

                                                        {/* 处理消息 */}
                                                        <div
                                                            className={
                                                                styles.processingMessageSection
                                                            }
                                                        >
                                                            <Title
                                                                level={5}
                                                                className={
                                                                    styles.sectionTitleWithMargin
                                                                }
                                                            >
                                                                处理消息
                                                            </Title>
                                                            <div
                                                                className={
                                                                    styles.processingMessageContent
                                                                }
                                                            >
                                                                <Text type="warning">
                                                                    {documentDetail.message}
                                                                </Text>
                                                            </div>
                                                        </div>

                                                        {/* 附加信息 */}
                                                        {documentDetail.metadata && (
                                                            <div className={styles.metadataSection}>
                                                                <Title
                                                                    level={5}
                                                                    className={styles.sectionTitle}
                                                                >
                                                                    附加信息
                                                                </Title>
                                                                <div
                                                                    className={
                                                                        styles.metadataContent
                                                                    }
                                                                >
                                                                    <Text>
                                                                        {documentDetail.metadata}
                                                                    </Text>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {/* 切片信息 */}
                                                        <div className={styles.chunksSection}>
                                                            <div
                                                                className={
                                                                    styles.chunksTitleContainer
                                                                }
                                                            >
                                                                <Title
                                                                    level={5}
                                                                    className={
                                                                        styles.sectionTitleChunks
                                                                    }
                                                                >
                                                                    切片信息
                                                                    {documentDetail.contentList &&
                                                                        documentDetail.contentList
                                                                            .length > 0 && (
                                                                            <Text
                                                                                type="secondary"
                                                                                className={
                                                                                    styles.chunksCount
                                                                                }
                                                                            >
                                                                                （共{' '}
                                                                                {
                                                                                    documentDetail
                                                                                        .contentList
                                                                                        .length
                                                                                }{' '}
                                                                                条）
                                                                            </Text>
                                                                        )}
                                                                </Title>
                                                            </div>
                                                            {documentDetail.contentList &&
                                                            documentDetail.contentList.length >
                                                                0 ? (
                                                                <>
                                                                    <div
                                                                        className={
                                                                            styles.chunksListContainer
                                                                        }
                                                                    >
                                                                        <Space
                                                                            direction="vertical"
                                                                            size="small"
                                                                            className={
                                                                                styles.chunksSpace
                                                                            }
                                                                        >
                                                                            {getCurrentChunks().map(
                                                                                (chunk, index) => {
                                                                                    const actualIndex =
                                                                                        (chunkPagination.current -
                                                                                            1) *
                                                                                            chunkPagination.pageSize +
                                                                                        index;
                                                                                    return (
                                                                                        <div
                                                                                            key={
                                                                                                actualIndex
                                                                                            }
                                                                                            className={
                                                                                                styles.chunkCard
                                                                                            }
                                                                                        >
                                                                                            <div
                                                                                                className={
                                                                                                    styles.chunkHeader
                                                                                                }
                                                                                            >
                                                                                                切片
                                                                                                #
                                                                                                {actualIndex +
                                                                                                    1}
                                                                                            </div>
                                                                                            <div
                                                                                                className={
                                                                                                    styles.chunkContent
                                                                                                }
                                                                                            >
                                                                                                {
                                                                                                    chunk.content
                                                                                                }
                                                                                            </div>
                                                                                        </div>
                                                                                    );
                                                                                }
                                                                            )}
                                                                        </Space>
                                                                    </div>

                                                                    {/* 切片分页 - 只有多页时才显示 */}
                                                                    {chunkPagination.total >
                                                                        chunkPagination.pageSize && (
                                                                        <div
                                                                            className={
                                                                                styles.chunksPaginationContainer
                                                                            }
                                                                        >
                                                                            <Pagination
                                                                                current={
                                                                                    chunkPagination.current
                                                                                }
                                                                                simple
                                                                                pageSize={
                                                                                    chunkPagination.pageSize
                                                                                }
                                                                                total={
                                                                                    chunkPagination.total
                                                                                }
                                                                                onChange={
                                                                                    handleChunkPageChange
                                                                                }
                                                                                size="small"
                                                                            />
                                                                        </div>
                                                                    )}
                                                                </>
                                                            ) : (
                                                                <Empty description="暂无切片数据" />
                                                            )}
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <Empty description="加载文档详情中..." />
                                                )}
                                            </Spin>
                                        </div>
                                    </TabPane>

                                    <TabPane tab="编辑" key="detail">
                                        <div className={styles.tabPaneContentEdit}>
                                            <Spin spinning={detailLoading}>
                                                <DocumentDetailForm
                                                    documentDetail={documentDetail}
                                                    onSave={handleDocumentSave}
                                                    loading={editLoading}
                                                />
                                            </Spin>
                                        </div>
                                    </TabPane>
                                </Tabs>
                            ) : (
                                <Empty
                                    image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
                                    className={styles.emptyState}
                                    description={<div>请选择一个文档查看详情</div>}
                                />
                            )}
                        </Card>
                    </Col>
                </Row>
            </div>

            {/* 添加文档弹窗 */}
            <AddDocumentModal
                visible={addModalVisible}
                onCancel={() => setAddModalVisible(false)}
                onSuccess={handleAddDocumentSuccess}
                loading={loading}
                knowledgeBaseId={knowledgeBaseDefaults?.id}
                defaultValues={knowledgeBaseDefaults}
                defaultsLoading={defaultsLoading}
            />
        </>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(DocumentManagePage);
