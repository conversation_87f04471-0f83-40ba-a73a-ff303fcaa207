// Document item hover styles
.documentItem:hover {
  background-color: #f5f5f5 !important;
}

.documentItemSelected:hover {
  background-color: #1677ff !important;
}

// Main container
.mainContainer {
  padding: 0px 10px;
  background: #f0f2f5;
  height: calc(100vh - 64px);
}

// Top knowledge base card
.knowledgeBaseCard {
  margin-bottom: 10px;
  background: linear-gradient(135deg, #4F8FF7 0%, #3875f0 100%);
  border: none;
}

// Back button styles
.backButtonIcon {
  color: white;
  font-size: 18px;
}

.backButton {
  color: white;
  font-size: 16px;
  padding: 4px 8px;
}

// Header info styles
.headerInfo {
  display: flex;
  align-items: center;
}

.knowledgeBaseName {
  color: white;
  margin: 0;
}

.headerSpace {
  margin-top: 8px;
}

.headerText {
  color: rgba(255,255,255,0.8);
}

.headerTextWithMargin {
  color: rgba(255,255,255,0.8);
  margin-left: 5px;
}

// Add document button
.addDocumentButton {
  background-color: rgba(255,255,255,0.3);
  border-color: rgba(255,255,255,0.6);
}

// Main content row
.mainContentRow {
  height: calc(100vh - 170px);
}

// Search and filter styles
.searchFilterContainer {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
}

.searchContainer {
  flex: 1;
}

.searchInput {
  width: 100%;
}

// Filter popover content
.filterPopoverContent {
  min-width: 300px;
}

.filterLabel {
  font-size: 14px;
}

.filterRowMargin {
  margin-top: 8px;
}

.filterSelectWidth {
  width: 100%;
}

.filterActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.filterIcon {
  cursor: pointer;
  font-size: 16px;
  color: #777777;
}

// Document list card
.documentListCard {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

// List container
.listMainContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.listScrollContainer {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

// Table header
.tableHeader {
  display: flex;
  padding: 8px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  font-size: 14px;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.tableHeaderId {
  width: 40px;
  text-align: center;
}

.tableHeaderTitle {
  flex: 1;
}

.tableHeaderType {
  width: 60px;
  text-align: center;
}

.tableHeaderStatus {
  width: 80px;
  text-align: center;
}

.tableHeaderActions {
  width: 40px;
}

// Document list item styles
.documentListItem {
  cursor: pointer;
  padding: 5px 20px;
  border-radius: 8px;
  margin-bottom: 4px;
  border: none;
  transition: all 0.2s ease;
}

.documentListItemNormal {
  background-color: transparent;
  color: #333333;
  box-shadow: none;
}

.documentListItemSelected {
  background-color: #1677ff;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
}

.documentItemContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.documentItemLeft {
  flex: 1;
}

.documentItemTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}

.documentItemTitleNormal {
  color: #333333;
  font-weight: 400;
}

.documentItemTitleSelected {
  color: #ffffff;
  font-weight: 500;
}

.documentIdBadge {
  background-color: #f5f5f5;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.documentItemRight {
  display: flex;
  align-items: center;
  gap: 8px;
}

.documentTypeTag {
  min-width: 50px;
  text-align: center;
  display: inline-block;
}

// Dropdown menu item styles
.dropdownItemDisable {
  color: #faad14;
}

.dropdownItemEnable {
  color: #52c41a;
}

.dropdownItemDelete {
  color: #ff4d4f;
}

.dropdownIconMargin {
  margin-right: 8px;
}

.moreIcon {
  color: #999999;
}

.moreIconSelected {
  color: #ffffff;
}

// Pagination container
.paginationContainer {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

// Right panel card
.rightPanelCard {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Tabs styles
.tabsContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Tab pane content
.tabPaneContent {
  padding: 20px;
  height: calc(100vh - 230px);
  overflow-y: auto;
}

.tabPaneContentEdit {
  padding: 20px;
  height: calc(100vh - 260px);
  overflow-y: auto;
}

// Document detail container
.documentDetailContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// Basic info section
.basicInfoSection {
  flex-shrink: 0;
}

.sectionTitle {
  margin-bottom: 16px;
}

.sectionTitleWithMargin {
  margin: 16px 0;
}

.sectionTitleChunks {
  margin: 16px 0;
  flex-shrink: 0;
}

.descriptionsContainer {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.descriptionItem {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.descriptionItemContent {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

// Processing message section
.processingMessageSection {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.processingMessageContent {
  padding: 12px;
  background-color: #fff2e8;
  border: 1px solid #ffd591;
  border-radius: 6px;
  font-size: 14px;
  color: #d46b08;
  white-space: pre-wrap;
  word-break: break-word;
}

// Metadata section
.metadataSection {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.metadataContent {
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
  white-space: pre-wrap;
  word-break: break-word;
}

// Chunks section
.chunksSection {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.chunksTitleContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chunksCount {
  font-size: 12px;
  margin-left: 8px;
}

.chunksListContainer {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  margin-bottom: 16px;
}

.chunksSpace {
  width: 100%;
}

.chunkCard {
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;
}

.chunkHeader {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  font-weight: 500;
}

.chunkContent {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-word;
}

.chunksPaginationContainer {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
}

// Empty state
.emptyState {
  margin-top: 100px;
}

// Group info badge
.groupIdBadge {
  background-color: #6fa3f5a6;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
  border: 1px solid #6fa3f5;
}