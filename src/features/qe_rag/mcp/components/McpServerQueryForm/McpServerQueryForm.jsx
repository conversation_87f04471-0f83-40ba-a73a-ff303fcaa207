import { useState, useEffect, useMemo } from 'react';
import { Form, Input, Select, But<PERSON>, Card } from 'antd';
import { getJoinedList, getAllGroupsList } from 'COMMON/api/qe_rag/workgroup';
import styles from './McpServerQueryForm.module.less';

const { Option } = Select;

const McpServerQueryForm = ({ query, tabActive, onSearch, onCreate }) => {
    const [form] = Form.useForm();
    const [workgroups, setWorkgroups] = useState([]);
    const [workgroupsLoading, setWorkgroupsLoading] = useState(false);
    const [allWorkgroups, setAllWorkgroups] = useState([]);
    const [allWorkgroupsLoading, setAllWorkgroupsLoading] = useState(false);

    // 根据当前标签页计算展示的工作组列表
    const displayWorkgroups = useMemo(() => {
        return tabActive === 'all' ? allWorkgroups : workgroups;
    }, [tabActive, allWorkgroups, workgroups]);

    // 获取工作组列表
    const fetchWorkgroups = async () => {
        setWorkgroupsLoading(true);
        try {
            const response = await getJoinedList();
            setWorkgroups(response?.data || []);
        } catch (error) {
            console.error('获取工作组列表错误:', error);
        } finally {
            setWorkgroupsLoading(false);
        }
    };

    // 获取所有工作组列表
    const fetchAllWorkgroups = async () => {
        if (tabActive !== 'all') return;
        
        setAllWorkgroupsLoading(true);
        try {
            const response = await getAllGroupsList();
            if (response?.data) {
                setAllWorkgroups(response.data);
            }
        } catch (error) {
            console.error('获取所有工作组列表错误:', error);
        } finally {
            setAllWorkgroupsLoading(false);
        }
    };

    useEffect(() => {
        fetchWorkgroups();
        if (tabActive === 'all') {
            fetchAllWorkgroups();
        }
    }, [tabActive]);

    // 监听标签页变化，加载对应的工作组数据
    useEffect(() => {
        if (tabActive === 'all') {
            fetchAllWorkgroups();
        }
    }, [tabActive]);

    const handleSearch = () => {
        const values = form.getFieldsValue();
        onSearch(values);
    };

    const handleReset = () => {
        form.resetFields();
        onSearch({ name: '', groupId: '' });
    };

    return (
        <Card className={styles.queryContainer}>
            <Form
                form={form}
                layout="inline"
                className={styles.queryForm}
                initialValues={query}
            >
                <Form.Item 
                    label="server名称" 
                    name="name"
                    className={styles.formItemWide}
                >
                    <Input 
                        placeholder="根据server名称模糊查询" 
                        allowClear 
                        className={styles.wideInput}
                    />
                </Form.Item>
                
                <Form.Item 
                    label="工作组" 
                    name="groupId"
                    className={styles.formItemWide}
                >
                    <Select
                        placeholder="请选择工作组"
                        allowClear
                        loading={tabActive === 'all' ? allWorkgroupsLoading : workgroupsLoading}
                        className={styles.wideInput}
                    >
                        {displayWorkgroups.map(item => (
                            <Option key={item.id} value={item.id}>
                                {`${item.id} - ${item.name} (${item.business})`}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
                
                <Form.Item>
                    <Button type="primary" onClick={handleSearch} style={{ marginRight: 8 }}>
                        查询
                    </Button>
                    <Button onClick={handleReset}>
                        重置
                    </Button>
                </Form.Item>

                <Form.Item className={styles.createBtn}>
                    <Button type="primary" onClick={onCreate}>
                        新建
                    </Button>
                </Form.Item>
            </Form>
        </Card>
    );
};

export default McpServerQueryForm;
