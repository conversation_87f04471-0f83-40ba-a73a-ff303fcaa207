@import '../common.module.less';

.moduleContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.webPage {
    display: flex;
    flex: 1;
    border: none !important; /* 强制移除边框 */
    border-radius: 0 !important; /* 强制移除圆角 */
    position: relative;
}

.header {
    display: flex;
    font-size: 20px;
    font-weight: 500;
    margin: 7px;
    top: 5px;
}

.footer {
    padding: 10px 20px 15px;
    background: transparent;
    display: flex;
    gap: 20px;
    margin-bottom: 8px;
    .infoItem {
        display: flex;
        align-items: center;
        color: #888;

        .title {
            font-weight: bold;
            margin-right: 5px;
            white-space: nowrap;
        }
    }
}
.noContent {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
