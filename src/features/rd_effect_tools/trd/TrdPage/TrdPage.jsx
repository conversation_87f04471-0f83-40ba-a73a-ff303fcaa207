import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Tooltip, Popconfirm, Input, message, Divider, Drawer } from 'antd';
import {
    PlusOutlined,
    EyeInvisibleOutlined,
    EyeOutlined,
    InfoCircleOutlined,
    LeftOutlined,
    RightOutlined,
    EditOutlined
} from '@ant-design/icons';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { mdSerialize, mdDeserialize } from '@baidu/morpho-data-transform';
import { <PERSON><PERSON><PERSON>, Toolbar, Editor, createEditor, useStable } from '@baidu/morpho';
// utils
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import trdModel from 'COMMON/models/rd_effect_tools/trdModel';
import { getFormatTime } from 'COMMON/utils/dateUtils';
import { post } from 'COMMON/utils/requestUtils';
import { fileImageUpload, uploadContentToBos } from 'COMMON/utils/utils';
import { getDocInfoFromUrl } from 'FEATURES/front_qe_tools/case/demand/utils';
import { TESTONE_BASICURL } from 'COMMON/config/baseUrl';
// api
import { getBosToken } from 'COMMON/api/base/common';
import { updateTrd, getKirin, getTrdTree } from 'COMMON/api/rd_effect_tools/trd';
// components
import NoContent from 'COMMON/components/common/NoContent';
import styles from './TrdPage.module.less';

const POLL_INTERVAL = 3000; // 每3秒轮询一次

const TASK_STATUS = {
    ing: [0, 2, 3], // 任务状态：进行中
    done: [1, 4, 5, 6, 7], // 任务状态：已完成
    success: [5], // 任务状态：成功
    fail: [1, 4, 6, 7] // 任务状态：失败
};

function TrdPage(props) {
    const {
        loading,
        setLoading,
        curMrd,
        setCurMrd,
        curTask,
        refreshTask,
        reCreateTask,
        curTreeNodeId,
        mrdTree,
        setMrdTree,
        username,
        currentSpace
    } = props;
    const [showSidebar, setShowSidebar] = useState(true); // 控制侧边栏显示状态
    const editorMd = useStable(() =>
        createEditor({
            image: {
                upload: fileImageUpload,
                base64: false
            },
            embed: false
        })
    );

    const carddetailMd = useStable(() =>
        createEditor({
            image: {
                upload: fileImageUpload,
                base64: false
            },
            embed: false
        })
    );
    const timerRef = useRef(null); // 轮询定时器
    const [isEdit, setIsEdit] = useState(false); // 初始文档是否编辑状态
    const [title, setTitle] = useState(''); // 文档标题，初始化展示用
    const [inputValue, setInputValue] = useState(editorMd.htmlSerializer.deserialize('<div/>')); // 文档内容，初始化展示用
    const [trdDocCreatePath, setTrdDocCreatePath] = useState(
        localStorage?.getItem('trd_doc_create_path')
    );
    const [docLoading, setDocLoading] = useState(true);
    const [uriList, setUriList] = useState([]); // 存储URI列表
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [currentUri, setCurrentUri] = useState('');
    // drawer详情json内容，初始化
    const [matchedCardDetail, setMatchedCardDetail] = useState(
        carddetailMd.htmlSerializer.deserialize('<div/>')
    );
    const [manualInterfaces, setManualInterfaces] = useState([]); // 已添加的人工接口
    const [searchResults, setSearchResults] = useState([]); // 搜索结果
    const [searchValue, setSearchValue] = useState(''); // 搜索框值
    const [isSearching, setIsSearching] = useState(false); // 是否正在搜索
    const [selectedInterface, setSelectedInterface] = useState(null);

    const getUrls = async (trdFile) => {
        let _url = await getBosToken({ bosLink: trdFile });
        const jsonDetail = await fetch(trdFile + '?authorization=' + _url.token).then((r) =>
            r.json()
        );
        // 提取并存储 apiCards 数据
        const cards =
            jsonDetail.apiCards?.map((card) => ({
                icodePath: card.icodePath,
                cardName: card.cardName,
                cardType: card.cardType
            })) || [];
        setUriList(cards.map((card) => card.cardName)); // 更新URI列表状态
    };
    const handleCardDetail = async (cardName) => {
        setCurrentUri(cardName);
        setMatchedCardDetail(carddetailMd.htmlSerializer.deserialize('<div/>')); // 清空之前的内容
        setDrawerVisible(true);

        try {
            // 1. 匹配当前需求节点
            const currentMrd = mrdTree?.find((item) => item.docNodeId === curMrd?.docNodeId);
            if (!currentMrd) {
                setMatchedCardDetail('未找到对应需求节点');
                return;
            }

            // 2. 查找匹配卡片
            let matchedCard =
                currentMrd.cardList?.aiCards?.find((card) => card.cardName === cardName) ||
                currentMrd.cardList?.manualCards?.find((card) => card.cardName === cardName);

            if (!matchedCard?.cardDetail || matchedCard?.cardDetail === null) {
                setMatchedCardDetail('该卡片无详情内容');
                return;
            }
            // 3. 获取带鉴权的BOS链接
            let bosToken = await getBosToken({ bosLink: matchedCard.cardDetail });
            const markdown = await fetch(
                matchedCard.cardDetail + '?authorization=' + bosToken.token
            ).then((r) => r.json());
            let markdownContent = markdown?.json ?? mdDeserialize(markdown?.content);
            setMatchedCardDetail(markdownContent);
            setLoading(false);
        } catch (error) {
            setMatchedCardDetail(
                carddetailMd.htmlSerializer.deserialize(`<div>加载失败: ${error.message}</div>`)
            );
        }
    };

    useEffect(() => {
        if (isEmpty(curTask)) {
            return;
        }
        if (TASK_STATUS?.success?.includes(curTask?.taskStatus)) {
            getEditorView();
        }
        getUrls(curTask?.taskResult?.trdFile);
    }, [curTask, curMrd?.kuLink]);

    // 轮询逻辑
    useEffect(() => {
        // 清除旧的定时器
        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }
        // 启动轮询
        if (curTask && TASK_STATUS?.ing?.includes(curTask?.taskStatus)) {
            timerRef.current = setInterval(() => {
                refreshTask(curMrd?.docNodeId);
            }, POLL_INTERVAL);
        }

        // 清理函数：组件卸载或 curTask 变化时执行
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        };
    }, [curTask?.taskId, curMrd?.docNodeId]);

    const statusInfo = useMemo(() => {
        if (!curTask?.taskStatus) {
            return {
                code: -2,
                msg: '暂无任务'
            };
        }
        switch (curTask?.taskStatus) {
            case 0:
                return {
                    code: 0,
                    msg: '任务已创建，待执行'
                };
            case 1:
                return {
                    code: -1,
                    msg: '任务创建失败，Kirin 创建失败'
                };
            case 2:
                return {
                    code: 1,
                    msg: 'Kirin 创建成功，任务执行中...'
                };
            case 3:
                return {
                    code: 1,
                    msg: '任务执行中，预计 10 分钟可完成...'
                };
            case 4:
                return {
                    code: -1,
                    msg: '任务执行失败'
                };
            case 5:
                return {
                    code: 2,
                    msg: '任务执行成功'
                };
            case 6:
                return {
                    code: -1,
                    msg: '任务执行失败，Kirin 执行超时'
                };
            case 7:
                return {
                    code: -1,
                    msg: '任务执行失败，执行超时'
                };
            default:
                return {
                    code: 0,
                    msg: '任务获取中...'
                };
        }
    }, [curTask?.taskStatus]);

    const getEditorView = async () => {
        try {
            // 获取生成任务内容
            let trdFile = curTask?.taskResult?.trdFile;
            let _url = await getBosToken({ bosLink: trdFile });
            const jsonDetail = await fetch(trdFile + '?authorization=' + _url.token).then((r) =>
                r.json()
            );
            setTitle(jsonDetail?.title ?? '无标题文档');
            let _value = jsonDetail?.json ?? mdDeserialize(jsonDetail?.content);
            setInputValue(_value);
            setDocLoading(false);
        } catch (e) {
            setDocLoading(false);
        }
    };

    // 创建文档
    const handleCreateDoc = async () => {
        const docInfo = getDocInfoFromUrl(trdDocCreatePath);
        const repoParams = {
            spaceGuid: docInfo.spaceGuid,
            groupGuid: docInfo.groupGuid,
            repositoryGuid: docInfo.repositoryGuid,
            parentDocGuid: docInfo?.docGuid
        };
        // 更新文档内容
        localStorage.setItem('trd_doc_create_path', trdDocCreatePath);
        let res = await post(
            TESTONE_BASICURL + '/api/ku/createKuDoc?username=testOne&token=testOne',
            {
                ...repoParams,
                createMode: 2,
                title: (title || '无标题文档') + getFormatTime(),
                contentInfo: {
                    protocol: 2,
                    contentType: 1,
                    text: mdSerialize(inputValue, {
                        standard: true // 必须要有
                    })
                }
            },
            {
                headers: {
                    username: 'lishuang30',
                    token: '35598e041a2a09b95d9d7886dae42411'
                }
            }
        );
        // 上传trd内容到bos
        await uploadTrdContent(title, res?.result?.url);
        message.success('文档创建成功');
        setIsEdit(false);
    };

    // 上传trd内容到bos
    const uploadTrdContent = async (_title = title, kuLink) => {
        let jsonData = {
            title: _title,
            content: mdSerialize(inputValue, {
                standard: true // 必须要有
            }),
            json: inputValue
        };
        let { url } = await uploadContentToBos(jsonData, 'json', false);
        await updateTrd({
            docNodeId: curMrd?.docNodeId,
            trdContent: url,
            kuLink: kuLink ?? ''
        });
        setCurMrd({
            ...curMrd,
            kuUpdateTime: new Date().getTime(),
            trdContent: url,
            kuLink: kuLink ?? ''
        });
    };
    const handleToggleHide = async (cardName, hideStatus) => {
        // 1. 更新本地状态
        const updatedMrdTree = mrdTree.map((item) => {
            if (item.docNodeId === curMrd?.docNodeId) {
                const updatedAiCards = item.cardList?.aiCards?.map((card) => {
                    if (card.cardName === cardName) {
                        return {
                            ...card,
                            cardHide: hideStatus ? 1 : 0
                        };
                    }
                    return card;
                });
                const updatedManualCards = item.cardList?.manualCards?.map((card) => {
                    if (card.cardName === cardName) {
                        return {
                            ...card,
                            cardHide: hideStatus ? 1 : 0
                        };
                    }
                    return card;
                });

                return {
                    ...item,
                    cardList: {
                        ...item.cardList,
                        aiCards: updatedAiCards,
                        manualCards: updatedManualCards
                    }
                };
            }
            return item;
        });

        setMrdTree(updatedMrdTree);

        // 2. 更新数据库
        const currentMrd = updatedMrdTree.find((item) => item.docNodeId === curMrd?.docNodeId);
        await updateTrd({
            docNodeId: curMrd?.docNodeId,
            cardList: currentMrd.cardList
        });

        // 3. 更新当前MRD状态
        setCurMrd({
            ...curMrd,
            cardList: currentMrd.cardList
        });

        message.success(hideStatus ? '卡片已隐藏' : '卡片已恢复显示');
    };

    // 获取人工接口全集
    const fetchAllInterfaces = async () => {
        const response = await getKirin({
            parameter: JSON.stringify({
                taskEnv: 'online',
                taskName: 'PRODUCT_TO_URI',
                // productId: currentSpace?.id,
                productId: 37,
                iCafeId: curMrd?.nodeLink,
                username: username || 'songxinglong'
            }),
            name: 'xiaolihua',
            token: '190df4f275dd42a7b4716d8e56571743'
        });
        const parsed = JSON.parse(response.response); // 这里关键
        // 获取当前MRD的AI关联接口列表
        const currentMrd = mrdTree?.find((item) => item.docNodeId === curMrd?.docNodeId);
        const aiCards = currentMrd?.cardList?.aiCards || [];

        // 过滤掉AI关联接口
        return (
            parsed?.response?.taskResult?.filter(
                (item) => !aiCards.some((card) => card.cardName === item.uri)
            ) || []
        );
    };

    // 人工关联接口模糊搜索处理
    const handleSearch = async (value) => {
        setSearchValue(value);
        if (!value.trim()) {
            setSearchResults([]);
            return;
        }

        setIsSearching(true);
        try {
            const allInterfaces = await fetchAllInterfaces();
            const filtered = allInterfaces.filter(
                (item) =>
                    item.uri.toLowerCase().includes(value.toLowerCase()) &&
                    !manualInterfaces.some((added) => added.uri === item.uri) // 新增过滤条件
            );
            setSearchResults(filtered);
        } finally {
            setIsSearching(false);
        }
    };

    // 添加接口
    const handleAddInterface = async (interfaceItem) => {
        // 1. 显示加载中消息
        const hide = message.loading('正在添加接口...', 0); // 0表示不自动关闭
        // 1. 更新本地状态
        const updatedMrdTree = mrdTree.map((item) => {
            if (item.docNodeId === curMrd?.docNodeId) {
                const newManualCard = {
                    cardName: interfaceItem.uri,
                    icodePath: interfaceItem.iCodePath,
                    cardType: 'NEW',
                    cardHide: 0
                };

                return {
                    ...item,
                    cardList: {
                        ...item.cardList,
                        manualCards: [...(item.cardList?.manualCards || []), newManualCard]
                    }
                };
            }
            return item;
        });

        setMrdTree(updatedMrdTree);
        setManualInterfaces((prev) => [...prev, interfaceItem]);
        setSearchValue('');
        setSearchResults([]);
        // 2. 更新数据库
        await updateTrd({
            docNodeId: curMrd?.docNodeId,
            cardList: updatedMrdTree.find((item) => item.docNodeId === curMrd?.docNodeId).cardList
        });

        // 3. 刷新数据并等待完成
        const { tree } = await getTrdTree({ treeNodeId: curTreeNodeId });
        setMrdTree(tree);

        // 4. 更新当前MRD引用
        const updatedMrd = tree.find((item) => item.docNodeId === curMrd.docNodeId);
        setCurMrd(updatedMrd);

        setTimeout(() => {
            hide(); // 先关闭加载中消息
            message.success('接口添加成功');
        }, 5000);
    };

    // 在组件加载时初始化已添加的人工接口
    useEffect(() => {
        if (mrdTree && curMrd?.docNodeId) {
            const currentMrd = mrdTree.find((item) => item.docNodeId === curMrd.docNodeId);
            if (currentMrd?.cardList?.manualCards) {
                const interfaces = currentMrd.cardList.manualCards.map((card) => ({
                    uri: card.cardName,
                    iCodePath: card.icodePath
                }));
                setManualInterfaces(interfaces);
            }
        }
    }, [mrdTree, curMrd?.docNodeId]);

    // 同步文档到知识库
    const renderSyncDoc = useCallback(
        (children) => {
            return (
                <Popconfirm
                    placement="left"
                    title="确定要同步当前内容到知识库吗？"
                    description={
                        <div className={styles.popconfirmContent}>
                            <Input
                                addonBefore="指定 Ku 位置"
                                placeholder="请指定文档创建的位置"
                                value={trdDocCreatePath}
                                onChange={(e) => {
                                    setTrdDocCreatePath(e.target.value);
                                }}
                                onBlur={() => setTrdDocCreatePath(trdDocCreatePath)} // 失焦时提交
                            />
                        </div>
                    }
                    okText="创建"
                    cancelText="取消"
                    onConfirm={handleCreateDoc}
                >
                    {children}
                </Popconfirm>
            );
        },
        [title, inputValue, trdDocCreatePath]
    );

    // 渲染按钮区域
    const renderButtonArea = useCallback(() => {
        let jsx = [];
        // 如果任务完成，则显示重新生成按钮
        if (TASK_STATUS?.fail?.includes(curTask?.taskStatus)) {
            return (
                <div className={styles.operatorItems}>
                    <Tooltip title="拉取最新卡片信息，重新触发生成">
                        <Button
                            size="small"
                            loading={loading}
                            type="primary"
                            className={styles.operatorItem}
                            onClick={async () => {
                                try {
                                    setLoading(true);
                                    await reCreateTask();
                                    setLoading(false);
                                } catch {
                                    setLoading(false);
                                }
                            }}
                        >
                            重新生成
                        </Button>
                    </Tooltip>
                </div>
            );
        }
        // 如果任务不成功，则不显示按钮
        if (!TASK_STATUS?.success?.includes(curTask?.taskStatus)) {
            return;
        }
        if (docLoading) {
            return (
                <div className={styles.operatorItems}>
                    <Button size="small" loading type="primary" className={styles.operatorItem}>
                        加载中...
                    </Button>
                </div>
            );
        }
        if (TASK_STATUS?.success?.includes(curTask?.taskStatus)) {
            return (
                <div className={styles.operatorItems}>
                    <Tooltip title="平台修改，暂不支持同步到知识库">
                        <Button
                            size="small"
                            type="primary"
                            onClick={async () => {
                                if (isEdit) {
                                    await uploadTrdContent(title);
                                    message.success('保存成功');
                                }
                                setIsEdit(!isEdit);
                            }}
                            className={styles.editButton}
                            icon={<EditOutlined />} // 添加编辑图标
                        >
                            {isEdit ? '更新内容' : '编辑'}
                        </Button>
                    </Tooltip>
                    {!isEdit && (
                        <>
                            {renderSyncDoc(
                                <Button size="small" className={styles.customButton}>
                                    同步到知识库
                                </Button>
                            )}
                            <Divider type="vertical" />
                            <Tooltip title="拉取最新卡片信息，重新触发生成" />
                        </>
                    )}
                </div>
            );
        }
        return jsx;
    }, [docLoading, curTask, getEditorView]);

    return (
        <div className={styles.moduleContainer}>
            <div className={styles.mainContent}>
                <div className={styles.webPage}>
                    {curTask?.taskStatus !== 5 && (
                        <div>
                            <NoContent
                                className={styles.statusInfo}
                                text={
                                    <Tooltip title={`任务 ID：${curTask?.taskId}`}>
                                        <span
                                            className={classnames({
                                                [styles.statusInfoIng]: [0, 1]?.includes(
                                                    statusInfo?.code
                                                ),
                                                [styles.statusInfoSuccess]: statusInfo?.code === 2,
                                                [styles.statusInfoError]: statusInfo?.code === -1
                                            })}
                                        >
                                            {statusInfo?.msg}
                                        </span>
                                    </Tooltip>
                                }
                            />
                        </div>
                    )}
                    {curTask?.taskStatus === 5 && (
                        <div className={styles.editor}>
                            <div className={styles.docHeader}>
                                <div className={styles.docHeaderContainer}>
                                    {isEdit ? (
                                        <Input
                                            placeholder="请输入标题"
                                            value={title}
                                            variant="borderless"
                                            onChange={(e) => setTitle(e.target.value)}
                                            onBlur={() => setTitle(title)}
                                        />
                                    ) : (
                                        <span className={classnames(styles.docTitle)}>{title}</span>
                                    )}
                                    <div className={styles.buttonArea}>{renderButtonArea()}</div>
                                </div>
                            </div>
                            <div className={styles.morpho}>
                                <Morpho editor={editorMd}>
                                    {isEdit && <Toolbar className={styles.editorToolbar} />}
                                    <Editor
                                        style={{
                                            overflow: 'scroll',
                                            marginTop: '10px',
                                            maxWidth: '100%'
                                        }}
                                        autoFocus
                                        value={inputValue}
                                        onChange={setInputValue}
                                        readOnly={!isEdit}
                                    />
                                </Morpho>
                            </div>
                        </div>
                    )}
                </div>
                <div className={styles.footer}>
                    <span className={styles.infoItem}>
                        <span className={styles.title}>知识库链接:</span>
                        {!isEmpty(curMrd?.kuLink) ? (
                            <a href={curMrd?.kuLink} target="_blank" rel="noopener noreferrer">
                                {title}
                            </a>
                        ) : (
                            <span className={styles.infoDefaultContent}>
                                {isEdit && '编辑中，不可同步'}
                                {TASK_STATUS?.ing?.includes(curTask?.taskStatus) &&
                                    '任务执行中，不可同步'}
                                {TASK_STATUS?.fail?.includes(curTask?.taskStatus) &&
                                    '任务执行失败，不可同步'}
                                {!isEdit && TASK_STATUS?.success?.includes(curTask?.taskStatus) && (
                                    <>
                                        待同步文档到知识库 ,
                                        {renderSyncDoc(
                                            <a className={styles.infoDefaultContentLink}>
                                                点击同步
                                            </a>
                                        )}
                                    </>
                                )}
                            </span>
                        )}
                    </span>
                    <span className={styles.infoItem}>
                        <span className={styles.title}>最新同步:</span>
                        {curMrd?.kuUpdateTime ? getFormatTime(curMrd?.kuUpdateTime) : '暂无'}
                    </span>
                </div>
            </div>
            {showSidebar ? (
                <div className={styles.sidebar}>
                    <div className={styles.sidebarHeader}>
                        任务卡
                        <Button
                            type="text"
                            className={styles.closeButton}
                            onClick={() => setShowSidebar(false)}
                            icon={<RightOutlined />}
                        />
                    </div>
                    <div className={styles.sidebarSection}>
                        <div className={classnames(styles.sidebarHeader, styles.noUnderline)}>
                            AI 关联接口
                        </div>
                        {uriList.length > 0 ? (
                            <div>
                                {uriList
                                    .map((cardName) => {
                                        const card =
                                            mrdTree
                                                ?.find(
                                                    (item) => item.docNodeId === curMrd?.docNodeId
                                                )
                                                ?.cardList?.aiCards?.find(
                                                    (card) => card.cardName === cardName
                                                ) ||
                                            mrdTree
                                                ?.find(
                                                    (item) => item.docNodeId === curMrd?.docNodeId
                                                )
                                                ?.cardList?.manualCards?.find(
                                                    (card) => card.cardName === cardName
                                                );
                                        return { cardName, isHidden: card?.cardHide === 1 };
                                    })
                                    .sort((a, b) => {
                                        if (a.isHidden && !b.isHidden) {
                                            return 1;
                                        }
                                        if (!a.isHidden && b.isHidden) {
                                            return -1;
                                        }
                                        return 0;
                                    })
                                    .map(({ cardName, isHidden }) => (
                                        <div
                                            key={cardName}
                                            className={classnames(styles.cardItem, {
                                                [styles.hidden]: isHidden
                                            })}
                                        >
                                            <span>{cardName}</span>
                                            <div className={styles.actionButtons}>
                                                <InfoCircleOutlined
                                                    onClick={() => handleCardDetail(cardName)}
                                                    style={{ cursor: 'pointer' }}
                                                    title="查看详情"
                                                />
                                                {isHidden ? (
                                                    <EyeOutlined
                                                        onClick={() =>
                                                            handleToggleHide(cardName, false)
                                                        }
                                                        style={{
                                                            cursor: 'pointer',
                                                            color: '#ff4d4f'
                                                        }}
                                                        title="撤销隐藏"
                                                    />
                                                ) : (
                                                    <EyeInvisibleOutlined
                                                        onClick={() =>
                                                            handleToggleHide(cardName, true)
                                                        }
                                                        style={{ cursor: 'pointer' }}
                                                        title="隐藏此卡片"
                                                    />
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                <Drawer
                                    title={currentUri}
                                    placement="right"
                                    width="80%"
                                    onClose={() => setDrawerVisible(false)}
                                    open={drawerVisible}
                                    destroyOnClose={false} // 改为false保留DOM
                                    maskClosable={false} // 防止点击遮罩关闭
                                >
                                    <div style={{ height: '100%' }}>
                                        <Morpho editor={carddetailMd}>
                                            <Editor
                                                style={{
                                                    display: 'flex',
                                                    alignItems: 'center'
                                                }}
                                                autoFocus
                                                value={matchedCardDetail}
                                                readOnly
                                            />
                                        </Morpho>
                                    </div>
                                </Drawer>
                            </div>
                        ) : (
                            <div>暂无关联接口</div>
                        )}
                    </div>
                    <div className={styles.sidebarSection}>
                        <div className={classnames(styles.sidebarHeader, styles.noUnderline)}>
                            人工关联接口
                        </div>
                        <div className={styles.sidebarContent}>
                            {/* 已添加的接口列表 - 添加排序逻辑 */}
                            {manualInterfaces.length > 0 && (
                                <div style={{ marginBottom: 16 }}>
                                    {manualInterfaces
                                        .map((item) => {
                                            const currentMrd = mrdTree?.find(
                                                (item) => item.docNodeId === curMrd?.docNodeId
                                            );
                                            const card = currentMrd?.cardList?.manualCards?.find(
                                                (card) => card.cardName === item.uri
                                            );
                                            return { ...item, isHidden: card?.cardHide === 1 };
                                        })
                                        .sort((a, b) => {
                                            if (a.isHidden && !b.isHidden) {
                                                return 1;
                                            }
                                            if (!a.isHidden && b.isHidden) {
                                                return -1;
                                            }
                                            return 0;
                                        })
                                        .map((item) => (
                                            <div
                                                key={item.uri}
                                                className={classnames(styles.cardItem, {
                                                    [styles.hidden]: item.isHidden // 这里会应用相同的删除线样式
                                                })}
                                            >
                                                <Tooltip title={item.uri}>
                                                    <span>{item.uri}</span>
                                                </Tooltip>
                                                <div className={styles.actionButtons}>
                                                    <InfoCircleOutlined
                                                        onClick={() => handleCardDetail(item.uri)}
                                                        style={{ cursor: 'pointer' }}
                                                        title="查看详情"
                                                    />
                                                    {item.isHidden ? (
                                                        <EyeOutlined
                                                            onClick={() =>
                                                                handleToggleHide(item.uri, false)
                                                            }
                                                            style={{
                                                                cursor: 'pointer',
                                                                color: '#ff4d4f'
                                                            }}
                                                            title="撤销隐藏"
                                                        />
                                                    ) : (
                                                        <EyeInvisibleOutlined
                                                            onClick={() =>
                                                                handleToggleHide(item.uri, true)
                                                            }
                                                            style={{ cursor: 'pointer' }}
                                                            title="隐藏此卡片"
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            )}

                            {/* 搜索框和添加按钮 - 保持不变 */}
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <Input.Search
                                    placeholder="搜索接口URI"
                                    value={searchValue}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    onBlur={() => handleSearch(searchValue)} // 失焦时触发搜索
                                    loading={isSearching}
                                    style={{ flex: 1 }}
                                />
                                <Button
                                    type="primary"
                                    icon={<PlusOutlined />}
                                    onClick={() => {
                                        if (selectedInterface) {
                                            handleAddInterface(selectedInterface);
                                            setSelectedInterface(null);
                                        }
                                    }}
                                    disabled={!selectedInterface}
                                    style={{ marginLeft: 8 }}
                                />
                            </div>

                            {/* 搜索结果下拉框 - 保持不变 */}
                            {searchResults.length > 0 && (
                                <div
                                    style={{
                                        marginTop: 8,
                                        maxHeight: 200,
                                        overflowY: 'auto',
                                        border: '1px solid #d9d9d9',
                                        borderRadius: 4
                                    }}
                                >
                                    {searchResults.map((item) => (
                                        <div
                                            key={item.uri}
                                            style={{
                                                padding: '8px 12px',
                                                cursor: 'pointer',
                                                borderBottom: '1px solid #f0f0f0',
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                ':hover': {
                                                    backgroundColor: '#f5f5f5'
                                                }
                                            }}
                                            onClick={() => setSelectedInterface(item)}
                                            className={
                                                selectedInterface?.uri === item.uri
                                                    ? 'selected-row'
                                                    : ''
                                            }
                                        >
                                            <Tooltip title={item.uri}>
                                                <span
                                                    style={{
                                                        flex: 1,
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis'
                                                    }}
                                                >
                                                    {item.uri}
                                                </span>
                                            </Tooltip>
                                            <Tooltip title={item.iCodePath}>
                                                <span
                                                    style={{
                                                        color: '#888',
                                                        marginLeft: 8,
                                                        maxWidth: '40%',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis'
                                                    }}
                                                >
                                                    {item.iCodePath}
                                                </span>
                                            </Tooltip>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                    <div className={styles.fixedBottom}>
                        <Button
                            size="small"
                            loading={loading}
                            className={styles.customButton}
                            type="primary"
                            block
                            onClick={async () => {
                                try {
                                    setLoading(true);
                                    await reCreateTask();
                                    setLoading(false);
                                } catch {
                                    setLoading(false);
                                }
                            }}
                        >
                            重新生成
                        </Button>
                    </div>
                </div>
            ) : (
                <Button
                    type="text"
                    className={styles.expandButton}
                    onClick={() => setShowSidebar(true)}
                    icon={<LeftOutlined />}
                />
            )}
        </div>
    );
}

export default connectModel([baseModel, trdModel], (state) => ({
    kuSDK: state.common.base.kuSDK,
    token: state.common.base.token,
    curTreeNodeId: state.common.trd.curTreeNodeId,
    cards: state.common.trd.cards,
    mrdTree: state.common.trd.mrdTree,
    username: state.common.base.username,
    currentSpace: state.common.base.currentSpace
}))(TrdPage);
