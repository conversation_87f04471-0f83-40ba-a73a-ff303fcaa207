@import '../common.module.less';
@import 'FEATURES/rd_effect_tools/trd/MrdPage/MrdPage.module.less';

.statusInfo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
}

.statusInfoIng {
    color: var(--primary-color);
}

.statusInfoError {
    color: var(--error-color);
}

.popconfirmContent {
    width: 350px;
    padding: 10px 0 5px 0;
}

.moduleContainer {
    display: flex;
    flex-direction: row;
    height: 100%;
}

.mainContent {
    flex: 1;
    min-width: 0;
    margin-bottom: 20px;
}
.sidebar {
    position: relative; // 添加相对定位
    width: 400px;
    height: 100%; /* 使用百分比高度 */
    background-color: var(--layout-background-color);
    margin-left: 1px;
    padding: 15px;
    overflow-y: auto; /* 添加滚动条以防内容溢出 */
    display: block;
    > * {
        margin-bottom: 16px; // 替代gap效果
        &:last-child {
            margin-bottom: 0;
        }
    }

    .fixedBottom {
        position: absolute; /* 改为绝对定位 */
        bottom: 0;
        left: 0;
        right: 0;
        padding: 16px 100px; /* 保持与父容器一致的padding */
        background-color: var(--layout-background-color); /* 保持与父容器一致的背景色 */
    }
}
.sidebarHeader {
    color: #1890ff; /* 蓝色字体 */
    padding: 2px 0; /* 调整内边距 */
    font-size: 16px;
    font-weight: 500;
    text-align: left; /* 左对齐 */
    position: relative; /* 为下划线定位 */
    margin-bottom: 15px; /* 增加底部间距 */
    display: flex;
    justify-content: space-between;
    align-items: center;

    &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -4px;
        width: 15%;
        height: 2px;
        background-color: #1890ff; /* 蓝色直线 */
    }
    // 添加无下划线变体
    &.noUnderline::after {
        display: none; // 完全移除下划线
    }
}

.sidebarSection {
    background: white;
    border-radius: 4px;
    padding: 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    min-height: 0; /* 允许内容压缩 */
}

.sidebarContent {
    min-height: 50px; /* 最小高度 */
    height: auto; /* 自适应高度 */
    overflow: hidden; /* 防止内容溢出 */
}

.docHeaderContainer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.buttonArea {
    margin-left: auto;
    margin-top: 10px;
    flex-shrink: 0;
}

.cardItem {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 4px;
    background-color: #f5f5f5;

    // 左侧文本样式
    > span:first-child {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;

        &:hover {
            overflow: visible;
            white-space: normal;
            background-color: #fff;
            z-index: 1;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 4px;
            border-radius: 4px;
        }
    }

    // 右侧按钮容器
    .actionButtons {
        display: flex;
        flex-shrink: 0;
        margin-left: 8px;

        > * {
            margin-left: 8px;
        }
    }
}

.hidden {
    text-decoration: line-through;
    opacity: 0.6;
}

.editButton {
    font-size: 14px;
    font-weight: 500;
    background: white; // 白色背景
    color: #1890ff; // 蓝色字体
    border: 1px solid #d9d9d9; // 添加边框
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0 10px 0 8px; // 左侧留出icon空间
    height: 32px;
    border-radius: 16px;
    z-index: 9999;
    display: flex;
    align-items: center;

    // 添加编辑图标
    .anticon-edit {
        margin-right: 4px;
        font-size: 14px;
    }

    &:hover {
        background: white;
        color: #40a9ff;
        border-color: #40a9ff;
    }

    &:active {
        background: white;
        color: #096dd9;
        border-color: #096dd9;
    }
}

.editButton,
.customButton {
    display: inline-flex; // 内联flex布局
    align-items: center; // 垂直居中
    white-space: nowrap; // 防止文字换行
}
