.moduleContainer {
    height: 100%;
    padding-top: 0px;
}

.header {
    position: relative;
    width: 100%;
    height: 30px;
    padding: 0 20px;

    .info {
        float: left;
        width: calc(100% - 200px);
        font-size: 12px;
        cursor: pointer;

        .infoItem {
            display: inline-block;
            padding: 2px 0;
            width: 100%;
        }

        .infoDefaultContent {
            color: var(--color2);
        }

        .infoDefaultContentLink {
            color: var(--primary-color);
        }

        .title {
            font-weight: bold;
            margin-right: 5px;
        }
    }

    .operator {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);

        .operatorItems {
            float: right;

            .operatorItem {
                margin-left: 10px;
            }
        }
    }
}

.webPage {
    position: relative;
    height: calc(100% - 60px);
    margin: 10px 20px 20px 20px;
    border: 1px solid #eee;
    border-radius: 15px;
    overflow: hidden;
}

.editor {
    position: relative;
    width: 100%;
    height: 100%;

    .morpho {
        height: 100%;
        overflow-y: scroll;

        :global {
            .mp-toolbar.mp-toolbar-fold:not(.mp-inline-toolbar),
            .mp-toolbar .mp-toolbar-inner,
            .mp-toolbar-fold-list .mp-toolbar-inner,
            .mp-editor-container-outer-no-title {
                padding: 0 !important;
            }

            .mp-editor-container-outer .mp-editor-container {
                padding: 0 10px !important;
            }
        }
    }

    .editorToolbar {
        width: 100%;
        position: absolute;
        top: 55px;
        left: 2px;
        height: 20px;
        justify-content: flex-start;
        margin-right: 10px;
        background-color: #fff !important;
        z-index: 999;
    }

    .docHeader {
        height: 80px;
        position: relative;
        width: 100%;
        background-color: #fff;

        :global {
            .ant-input {
                font-size: 36px !important;
                font-weight: bold;
            }
        }

        .docTitle {
            display: inline-block;
            max-width: 100%;
            height: 41px;
            padding-right: 20px;
            line-height: 41px;
            white-space: nowrap;
            /* 禁止自动换行 */
            overflow-x: auto;
            position: absolute;
            left: 11px;
            top: 4px;
            font-size: 30px;
            font-weight: bold;
            overflow-x: scroll;
        }
    }
}

.customButton {
    font-size: 14px;
    font-weight: 500;
    background: linear-gradient(to right, #69b1f4, #268cfa);
    color: white;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0 10px;
    height: 32px;
    border-radius: 16px; /* 更圆润的圆角 */
    z-index: 9999;
    &:hover {
        background: linear-gradient(to right, #40a9ff, #1890ff);
    }

    &:active {
        background: linear-gradient(to right, #096dd9, #0050b3);
    }
}
