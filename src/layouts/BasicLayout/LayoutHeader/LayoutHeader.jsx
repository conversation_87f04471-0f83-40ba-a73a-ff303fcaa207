import classnames from 'classnames';
import { Divider, Layout } from 'antd';
import qamateIcon from 'RESOURCES/img/qamateIcon.png';
import { HEADER_STYLE } from 'RESOURCES/css/common.style';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import {
    SpaceSelect,
    PersonalCenter,
    SetCenter,
    UserInfo,
    DeviceSetting,
    RunEnv,
    ModuleSelect
} from './components';
import styles from './LayoutHeader.module.less';

const { Header } = Layout;

const LayoutHeader = (props) => {
    const {
        extra,
        centerExtra,
        centerExtraStyles,
        rightExtra,
        curOsType,
        className,
        onChange,
        showRunSetting = false, // 是否显示环境运行设置
        showIcon = true, // 是否显示logo
        showUserInfo = false, // 是否显示用户信息
        showPersonalCenter = true, // 是否显示个人中心
        showSetCenter = true // 是否显示设置
    } = props;

    return (
        <Header style={HEADER_STYLE} className={classnames(styles.layoutHeader, className)}>
            {showIcon && (
                <a href={window.location.origin + '/#/case/index'}>
                    <img src={qamateIcon} height={22} width={22} className={styles.logo} />
                </a>
            )}
            {/* 切换模块 */}
            <ModuleSelect />
            <SpaceSelect onChange={onChange} extraStyle={{ maxWidth: '250px' }} />
            {extra}
            {centerExtra && (
                <div className={styles.centerExtra} style={{ ...centerExtraStyles }}>
                    {centerExtra}
                </div>
            )}
            <span className={styles.rightExtra}>
                {/* 运行环境 */}
                {showRunSetting && (
                    <>
                        <div className={styles.runSetting}>
                            {isElectron() && [1, 2].includes(curOsType) && (
                                <div className={styles.deviceOperator}>
                                    <DeviceSetting curOsType={curOsType} />
                                </div>
                            )}
                            {[1, 2, 4].includes(curOsType) && <RunEnv curOsType={curOsType} />}
                        </div>
                        <Divider type="vertical" />
                    </>
                )}
                {rightExtra && (
                    <>
                        {rightExtra}
                        <Divider type="vertical" />
                    </>
                )}
                {showUserInfo && <UserInfo />}
                {showSetCenter && <SetCenter />}
                {showPersonalCenter && <PersonalCenter />}
            </span>
        </Header>
    );
};

export default connectModel([baseModel], (state) => ({
    curOsType: state.common.case.curOsType
}))(LayoutHeader);
